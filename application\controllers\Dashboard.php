<?php defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        $this->load->model('parent_model');
        $this->load->model('dashboard_model');
        $this->load->model('taskstodo_model');
        $this->load->model('avatar');
        $this->load->model('bookroom_model');
        $this->load->model('timetable/staff_tt');
        $this->load->model('flash_model');
        $this->load->library('filemanager');
        $this->load->model('feesv2/fees_student_model');
		$this->load->model('gallery_model');
        $this->load->model('staff/Staff_leave', 'staff_leave');
        $this->load->model('Visitor_v2_model');
        $this->load->model('communication/texting_model');

        // Load the redirect helper
        $this->load->helper('redirect');
    }

    public function changeBranch() {
        $branch_id = $_POST['branch_id'];
        $this->session->set_userdata('selected_branch', $branch_id);
        echo 1;
    }

    public function checkPdfViewer() {
        $dashboard = 'inc/pdf_viewer';
        $data['main_content'] = $dashboard;
        $this->load->view('inc/template', $data);
    }

    // log the user in
    public function index()
    {
        $selectedAvatarType = $this->authorization->getAvatarType();
        switch ($selectedAvatarType) {
            case 1: //Student
                $this->__showStaffDashboard(); //Not used
                break;
            case 2: //Parent
                if(CONFIG_ENV['school_sub_domain'] === 'heliumacademy') {
                    redirect('helium/learning');
                }
                $parent_dashboardV3 = $this->settings->getSetting('parent_dashboard_v3');
                if($parent_dashboardV3) {
                    if($this->mobile_detect->isMobile()){
                        redirect('parentdashboard');
                    }else{
                        redirect('info_message');
                    }
                   
                }
                if($this->mobile_detect->isTablet()){
                    $this->__showMobileParentDashboardSkeleton_v2();
                }
                else if ($this->mobile_detect->isMobile()) {
                    $this->__showMobileParentDashboardSkeleton_v2();
                }
                else {
                    $this->__showDesktopParentDashboard();
                }                                            
                break;
            case 3: //Staff
            case 4: //Super Admin
                if ($this->mobile_detect->isMobile()) {
                    $this->__showMobileStaffDashboardSkeleton();
                    // $this->__showMobileStaffDashboard();
                } else {
                    $this->__showDesktopStaffDashboard();
                }
                break;
            case 5:
                if ($this->mobile_detect->isMobile()) {
                    $this->__showMobileDriverDashboard();
                } else {
                    $this->__showDesktopDriverDashboard();
                }
                break;
            default:
                //If avatar type does not match any of the above, logout
                js_logout('Some unexpected error occurred. Please re-login and try.');
        }
    }

    private function __constructAttendanceSummary()
    {
        $data['att_student_status'] = $this->dashboard_model->getAttendanceStudentsStatus();
        $data['att_data'] = $this->dashboard_model->getAttendanceStatus();
        $data['sectionName'] = $this->dashboard_model->get_sections_school();

        // echo "<pre>"; print_r($data['sectionName']); die();
        $data['numberOfStudents'] = $this->dashboard_model->getTotalStudents();
        // echo "<pre>"; print_r($data['numberOfStudents']); die();
        $data['absent'] = 0;
        $data['present'] = 0;
        foreach ($data['att_student_status'] as $key => $value) {
            if ($value['type'] == 'A')
                $data['absent'] = $value['att_count'];
            if ($value['type'] == 'P')
                $data['present'] = $value['att_count'];
        }
        $data['attNotTaken'] = $data['numberOfStudents'] - $data['absent'] - $data['present'];
        $data['totalSections'] = 0;
        $data['attTaken'] = 0;
        foreach ($data['att_data']['data'] as $key => $value) {
            foreach ($value as $k => $val) {
                $data['totalSections']++;
                if (isset($val->attendanceTaken) && $val->attendanceTaken == 'yes') {
                    $data['attTaken']++;
                }
            }
        }
        return $data;
    }

    private function __constructCTAttendanceSummary($sectionId)
    {
        $data['isClassTeacher'] = 1;
        $data['numberOfStudents'] = $this->dashboard_model->getTotalStudents($sectionId);
        $data['att_student_status'] = $this->dashboard_model->getAttendanceStudentsStatus($sectionId);
        $data['absent'] = $data['att_student_status'][0]['att_count'];
        $data['present'] = $data['att_student_status'][1]['att_count'];
        // $data['attTaken'] = ($data['absent']+$data['present'])?'Yes':'No';

        return $data;
    }

     private function __constructAttendanceSummaryV2($sectionId)
    {
        $data['isClassTeacher'] = 1;
        $data['numberOfStudents'] = $this->dashboard_model->getTotalStudents($sectionId);
        $attendanceResult = $this->dashboard_model->getAttendancev2StudentsStatus($sectionId);
        // echo "<pre>"; print_r($attendanceResult); die();

        $data['att_student_status'] = $attendanceResult['data'];
        $data['attendance_type'] = $attendanceResult['type'];
        $data['attendance_message'] = $attendanceResult['message'];

        // Handle different types of responses
        switch ($attendanceResult['type']) {
            case 'holiday':
                $data['absent'] = 0;
                $data['present'] = 0;
                $data['holiday'] = 1;
                $data['attTaken'] = 'Holiday';
                break;
            case 'event':
                $data['absent'] = 0;
                $data['present'] = 0;
                $data['event'] = 1;
                $data['attTaken'] = 'Event';
                break;
            case 'attendance':
                // Parse attendance data - it comes as array with 'type' and 'att_count'
                $absent_count = 0;
                $present_count = 0;

                if (isset($attendanceResult['data']) && is_array($attendanceResult['data'])) {
                    foreach ($attendanceResult['data'] as $att_data) {
                        if (isset($att_data['type']) && isset($att_data['att_count'])) {
                            if ($att_data['type'] == 'A') {
                                $absent_count = $att_data['att_count'];
                            } elseif ($att_data['type'] == 'P') {
                                $present_count = $att_data['att_count'];
                            }
                        }
                    }
                }

                $data['absent'] = $absent_count;
                $data['present'] = $present_count;
                $data['attTaken'] = 'Yes';
                break;
            case 'not_taken':
            case 'no_sections':
            default:
                $data['absent'] = 0;
                $data['present'] = 0;
                $data['attTaken'] = 'No';
                break;
        }
// echo "<pre>"; print_r($data); die();
        return $data;
    }

    private function __constructDailyEvents(){
        $dailyEvents = array();
        $dailyEventsData = $this->dashboard_model->getDailyEventsData();
        foreach ($dailyEventsData as $value) {
            $dailyEvents[$value->dueDateDisplay][] = $value;
        }
        $monthMap = [
            'Jan' => 1,
            'Feb' => 2,
            'Mar' => 3,
            'Apr' => 4,
            'May' => 5,
            'Jun' => 6,
            'Jul' => 7,
            'Aug' => 8,
            'Sep' => 9,
            'Oct' => 10,
            'Nov' => 11,
            'Dec' => 12,
        ];
        $currentMonth = date('M');
        function sortMonthsEvents($monthA, $monthB, $monthMap, $currentMonth) {
            $monthAIndex = $monthMap[substr($monthA, 0, 3)];
            $monthBIndex = $monthMap[substr($monthB, 0, 3)];
            
            $currentMonthIndex = $monthMap[$currentMonth];

            $monthAAdjusted = ($monthAIndex - $currentMonthIndex + 12) % 12;
            $monthBAdjusted = ($monthBIndex - $currentMonthIndex + 12) % 12;

            if ($monthAAdjusted == $monthBAdjusted) {
                return (int)substr($monthA, 4, 2) - (int)substr($monthB, 4, 2);
            }

            return $monthAAdjusted - $monthBAdjusted;
        }

        uksort($dailyEvents, function($a, $b) use ($monthMap, $currentMonth) {
            return sortMonthsEvents($a, $b, $monthMap, $currentMonth);
        });

        foreach ($dailyEvents as &$entries) {
            usort($entries, function($a, $b) {
                return strtotime($a->task_due_date) - strtotime($b->task_due_date);
            });
        }
        $data['dailyEvents'] = $dailyEvents;
        return $data;
    }

    private function __constructBirthdayList()
    {
        $student_view_birthdays=$this->settings->getSetting("student_view_birthdays");
        $staff_view_birthdays=$this->settings->getSetting("staff_view_birthdays");

        $data['studentDob'] = array();
        //getting student birthdays
        if($staff_view_birthdays || $student_view_birthdays){
            if ($student_view_birthdays) {
                // $data['stdJustCelebratedDob'] = $this->dashboard_model->get_studentWiseDataforJustCelebratedBirthdayList('widget_view');
                // $data['StdDob'] = $this->dashboard_model->get_studentDataforBirthdayList();
                // $data['StdDob'] = array_merge($data['StdDob'], $data['stdJustCelebratedDob']);
                $data['StdDob'] = $this->dashboard_model->get_student_birthday_data();
                foreach ($data['StdDob'] as $value) {
                    $data['studentDob'][$value->dobDisplay][] = $value;
                }
            }

            //getting staff  birthdays
            if ($staff_view_birthdays) {
                // $data['staffJustCelebratedDob'] = $this->dashboard_model->get_staffWiseJustCelebratedBirthdayList('widget_view');
                // $data['staffDob'] = $this->dashboard_model->get_staffDataforTodayBirthdayList();
                // $data['staffDob'] = array_merge($data['staffDob'], $data['staffJustCelebratedDob']);
                $data['staffDob'] = $this->dashboard_model->get_staff_birthday_data();
                foreach ($data['staffDob'] as $value) {
                    $data['studentDob'][$value->dobDisplay][] = $value;
                }
            }
        }else{
            //default option for birthdays

            //birthdays for students
            // $data['stdJustCelebratedDob'] = $this->dashboard_model->get_studentWiseDataforJustCelebratedBirthdayList('widget_view');
            // $data['StdDob'] = $this->dashboard_model->get_studentDataforBirthdayList();
            // $data['StdDob'] = array_merge($data['StdDob'], $data['stdJustCelebratedDob']);
            $data['StdDob'] = $this->dashboard_model->get_student_birthday_data();
            foreach ($data['StdDob'] as $value) {
                $data['studentDob'][$value->dobDisplay][] = $value;
            }

            //birthdays for staff
            // $data['staffJustCelebratedDob'] = $this->dashboard_model->get_staffWiseJustCelebratedBirthdayList('widget_view');
            // $data['staffDob'] = $this->dashboard_model->get_staffDataforTodayBirthdayList();
            // $data['staffDob'] = array_merge($data['staffDob'], $data['staffJustCelebratedDob']);
            $data['staffDob'] = $this->dashboard_model->get_staff_birthday_data();
            foreach ($data['staffDob'] as $value) {
                $data['studentDob'][$value->dobDisplay][] = $value;
            }
        }

        // foreach ($staffDob as $key => $value) {
        //     if (array_key_exists($today, $data['studentDob'])) {
        //         $data['studentDob'][$value->dobDisplay][] = $value;
        //     } else {
        //         $data['studentDob'] = array_reverse($data['studentDob']);
        //         $data['studentDob'][$value->dobDisplay][] = $value;
        //         $data['studentDob'] = array_reverse($data['studentDob']);
        //         break;
        //     }
        // }
        // ksort($data['studentDob']);
        uksort($data['studentDob'], function($a, $b) {
            return strtotime($a) - strtotime($b);
        });
        // echo "<pre>";print_r($data['studentDob']);die();
        // $monthMap = [
        //     'Jan' => 1,
        //     'Feb' => 2,
        //     'Mar' => 3,
        //     'Apr' => 4,
        //     'May' => 5,
        //     'Jun' => 6,
        //     'Jul' => 7,
        //     'Aug' => 8,
        //     'Sep' => 9,
        //     'Oct' => 10,
        //     'Nov' => 11,
        //     'Dec' => 12,
        // ];
        // $currentMonth = date('M');
        // function sortMonths($monthA, $monthB, $monthMap, $currentMonth) {
        //     $monthAIndex = $monthMap[substr($monthA, 0, 3)];
        //     $monthBIndex = $monthMap[substr($monthB, 0, 3)];
            
        //     $currentMonthIndex = $monthMap[$currentMonth];

        //     $monthAAdjusted = ($monthAIndex - $currentMonthIndex + 12) % 12;
        //     $monthBAdjusted = ($monthBIndex - $currentMonthIndex + 12) % 12;

        //     if ($monthAAdjusted == $monthBAdjusted) {
        //         return (int)substr($monthA, 4, 2) - (int)substr($monthB, 4, 2);
        //     }

        //     return $monthAAdjusted - $monthBAdjusted;
        // }

        // uksort($data['studentDob'], function($a, $b) use ($monthMap, $currentMonth) {
        //     return sortMonths($a, $b, $monthMap, $currentMonth);
        // });

        foreach ($data['studentDob'] as &$entries) {
            usort($entries, function($a, $b) {
                return strtotime($a->dob) - strtotime($b->dob);
            });
        }
        // echo "<pre>";print_r($data['studentDob']);die();
        return $data;
    }

    private function __showDesktopDriverDashboard()
    {
        $dashboard = 'dashboard/driver/_desktop_index';
        $data['main_content'] = $dashboard;
        $this->load->view('inc/template', $data);
    }

    private function __showMobileDriverDashboard()
    {
        $dashboard = 'dashboard/driver/_mobile_index';
        $data['main_content'] = $dashboard;
        $this->load->view('inc/template', $data);
    }

    private function __showDesktopStaffDashboard()
    {

        //Get all the permissions
        $data['permitFlashNews'] = $this->authorization->isModuleEnabled('FLASH_NEWS');
        $data['schoolInfoWidget'] = $this->authorization->isSuperAdmin();
        $data['student_catg_widget'] = $this->authorization->isSuperAdmin();
        $data['staff_catg_widget'] = $this->authorization->isAuthorized('WIDGET.STAFF_DATA');
        $data['permitOverallAttendanceSummary'] = $this->authorization->isModuleEnabled('STUDENT_ATTENDANCE') && $this->authorization->isAuthorized('STUDENT_ATTENDANCE.VIEW_SUMMARY');
        $data['permitOverallAttendanceV2Summary'] = $this->authorization->isModuleEnabled('STUDENT_ATTENDANCE_V2') && $this->authorization->isAuthorized('WIDGET.STUDENT_ATTENDANCE_V2_SUMMARY');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $data['permitCTAttendanceSummary'] =   $this->authorization->isModuleEnabled('STUDENT_ATTENDANCE') && ($this->dashboard_model->isClassTeacher($staffId) != -1);

        // V2 Attendance Summary Permission
        $data['permitCTAttendanceSummaryV2'] = $this->authorization->isModuleEnabled('STUDENT_DAY_ATTENDANCE_V2') &&
                                               ($this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ATTENDANCE') ||
                                                $this->authorization->isAuthorized('STUDENT_DAY_ATTENDANCE_V2.TAKE_ALL_SECTION_ATTENDANCE'));
        $data['permitBirthdayView'] = $this->authorization->isAuthorized('UPCOMING_BIRTHDAY.VIEW');
        $data['permitCalendarView'] = $this->settings->isModuleEnabled('SCHOOL_CALENDAR') && $this->authorization->isAuthorized('SCHOOL_CALENDAR.MODULE');
        $data['permitStaffLeaveView'] = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
        $data['permitFee'] = $this->authorization->isModuleEnabled('FEESV2') && $this->authorization->isAuthorized('FEESV2.WIDGET');
        $data['permitCirculars'] = $this->settings->isModuleEnabled('CIRCULAR') && $this->authorization->isAuthorized('CIRCULAR.STAFF_CIRCULAR');
        $data['permitCircularsV2'] = $this->settings->isModuleEnabled('CIRCULARS_V2');
        $data['permitStaffTT'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('WIDGET.MY_TIMETABLE');
        $data['permitTodo'] = $this->authorization->isModuleEnabled('TASKSTODO') && $this->authorization->isAuthorized('TODO.MODULE');
        $data['showStudentCount'] = $this->authorization->isAuthorized('STUDENT.MODULE');
        $data['showStaffCount'] = $this->authorization->isAuthorized('WIDGET.STAFF_DATA');
        $data['showSMSCount'] = $this->authorization->isModuleEnabled('TEXTING');

        $data['permit_staff_attendance_checkin_widget'] = $this->authorization->isAuthorized('WIDGET.ATTENDANCE_CHECKIN_WIDGET');

        $data['show_texting_widget'] = $this->authorization->isModuleEnabled('TEXTING_WIDGET');
        $data['show_circular_widget'] = $this->authorization->isModuleEnabled('CIRCULAR_WIDGET');

        $data['show_staff_data'] = $this->authorization->isAuthorized('WIDGET.STAFF_DATA');
        $data['show_student_data'] = $this->authorization->isAuthorized('WIDGET.STUDENT_DATA');
        $data['show_task_assigned'] = $this->authorization->isAuthorized('WIDGET.TASK_ASSIGNED');
        $data['show_staff_task_assigned'] = $this->authorization->isModuleEnabled('STAFF_TASKS_BASKET');
        $data['show_communication_data'] = $this->authorization->isAuthorized('WIDGET.COMMUNICATION_TREND');
        $data['show_task_summary'] = $this->authorization->isAuthorized('WIDGET.TASK_SUMMARY');
        $data['show_fee_collection_total'] = $this->authorization->isAuthorized('WIDGET.FEE_COLLECTION_TOTAL');
        $data['show_fee_collection_trend'] = $this->authorization->isAuthorized('WIDGET.FEE_COLLECTION_TREND');
        $data['show_reporting_manager'] = $this->authorization->isAuthorized('WIDGET.REPORTING_MANAGER');
        $data['student_count_widget'] = $this->authorization->isAuthorized('WIDGET.STUDENT_COUNT');
        $data['parent_ticketing_widget'] = $this->authorization->isAuthorized('WIDGET.PARENT_TICKETING');
        $data['enquiryWidget'] = $this->authorization->isAuthorized('WIDGET.ENQUIRY_WIDGET');
        $data['showOtherLinks'] = $this->authorization->isAuthorized('WIDGET.OTHER_LINKS');
        $data['permit_gallery'] = $this->authorization->isAuthorized('WIDGET.GALLERY');
        $data['student_nc'] = $this->authorization->isAuthorized('WIDGET.STUDENT_NON_COMPLIANCE');
        $data['student_nc_statistics'] = $this->authorization->isAuthorized('WIDGET.STUDENT_NON_COMPLIANCE_STATISTICS');
        $data['student_observation'] = $this->authorization->isAuthorized('WIDGET.STUDENT_OBSERVATION');
        $data['single_window'] = $this->authorization->isAuthorized('WIDGET.SINGLE_WINDOW');
        $data['student_councelling_trend'] = $this->authorization->isAuthorized('WIDGET.STUDENT_COUNCELLING_TREND');
        $data['internal_ticketing_widget'] = $this->authorization->isAuthorized('WIDGET.INTERNAL_TICKETING');
        $data['staff_on_leave_widget'] = $this->authorization->isAuthorized('WIDGET.STAFF_ON_LEAVE_WIDGET');
        $data['student_counselling'] = $this->settings->isModuleEnabled('STUDENET_COUNSELLING') && $this->authorization->isAuthorized('STUDENT_COUNSELLING.MODULE') && $this->authorization->isAuthorized('WIDGET.STUDENT_COUNSELLING_STAT');
        $data['student_count_widget_by_gender'] = $this->authorization->isAuthorized('WIDGET.STUDENT_COUNT_GENDERWISE');
        $data['student_widget_statistics'] = $this->authorization->isAuthorized('WIDGET.STUDENT_WIDGET_STATISTICS');
        $data['get_approval_widget_data'] = $this->authorization->isAuthorized('WIDGET.GET_APPROVAL_WIDGET_DATA');
        $data['staff_anniversary_widget_data'] = $this->authorization->isAuthorized('WIDGET.STAFF_ANNIVERSARY_WIDGET');
        $data['infirmary_visitor_widget_data'] = $this->authorization->isAuthorized('WIDGET.INFIRMARY_VISITOR_WIDGET_DATA');
        $data['infirmary_statistics'] = $this->authorization->isAuthorized('WIDGET.INFIRMARY_STATISTICS_WIDGET');
        $data['library_statistics'] = $this->authorization->isAuthorized('WIDGET.LIBRARY_STATISTICS_WIDGET');
        $data['transportation_statistics'] = $this->authorization->isAuthorized('WIDGET.TRANSPORTATION_STATISTICS_WIDGET');
        $data['inventory_statistics'] = $this->authorization->isAuthorized('WIDGET.INVENTORY_STATISTICS_WIDGET');
        $data['category_stock_details_widget'] = $this->authorization->isAuthorized('WIDGET.CATEGORY_STOCK_DETAILS_WIDGET');
        $data['books_trend_chart'] = $this->authorization->isAuthorized('WIDGET.BOOKS_TREND_WIDGET');
        $data['permission_visitor_data'] =$this->authorization->isAuthorized('WIDGET.VISITOR');
        $data['rfid_widget'] = $this->authorization->isAuthorized('WIDGET.RFID_WIDGET');
        $data['substitution_statistics'] = $this->authorization->isAuthorized('WIDGET.SUBSTITUTION_STATISTICS');
        $data['task_basket'] = $this->authorization->isAuthorized('WIDGET.TASK_BASKET');
        $data['dailyPlannerAndCalendar'] = $this->authorization->isModuleEnabled('DAILY_PLANNER') && $this->authorization->isAuthorized('DAILY_PLANNER.DAILY_PLANNER_CALENDAR') && $this->authorization->isAuthorized('WIDGET.DAILY_PLANNER');
        $data['staff_attendance_reports_wise'] = $this->authorization->isAuthorized('WIDGET.STAFF_ATTENDANCE_REPORTS_WISE');
        $data['simulate_504_timeout'] = $this->authorization->isModuleEnabled('SIMULATE_504_TIMEOUT');
        $data['show_my_interviews']= $this->dashboard_model->getInterviewerId($this->authorization->getAvatarStakeHolderId());
        if($this->authorization->getAvatarStakeHolderId() != 0){
            $data['staff_locked_unlocked_profile_status']= $this->dashboard_model->get_staff_locked_unlocked_profile_status($this->authorization->getAvatarStakeHolderId());
        }

        if ($data['student_count_widget_by_gender']) {
            $data['student_count_genderwise'] = $this->dashboard_model->getStudentCount();
        }

        if($data['single_window']){
            $data['single_window_count'] = $this->dashboard_model->get_single_window_count();
        }
        // echo '<pre>';print_r($data['single_window_count']);die();
        $data['student_checkin_tracking'] = $this->authorization->isAuthorized('WIDGET.STUDENT_CHECKIN_TRACKING');
        // if ($data['student_checkin_tracking']) {
        //     $data['student_tracking_count'] = $this->dashboard_model->student_tracking_count();
        // }

        $data['permit_staff_leaves_widget'] = $this->authorization->isAuthorized('WIDGET.STAFF_LEAVES_DETAILS');
        
        $data['permit_month_wise_staff_calender_widget'] = $this->authorization->isAuthorized('WIDGET.MONTH_WISE_STAFF_CALENDAR');

        $data['enquiry_stat_widget'] = $this->authorization->isAuthorized('WIDGET.ENQUIRY_STATISTICS_WIDGET');
        $data['admission_statistics_widget'] = $this->authorization->isAuthorized('WIDGET.ADMISSION_STATISTICS_WIDGET');
        
        if ($data['permitFlashNews'] && $this->settings->getSetting('birthday_flashnews_message')) {
            $data['birthday_news_staff'] = $this->flash_model->getBirthdayOfStaff();
        }
        
        if($data['showOtherLinks'] ){
            $this->load->model('Otherlinks_model');
            $adminPermission = $this->authorization->isAuthorized('OTHERLINKS.ADMIN');
            if ($adminPermission) {
               $data['links'] = $this->Otherlinks_model->getAllEnabledOtherLinks();
            }else{
                $data['links'] = $this->Otherlinks_model->getEnabledLinksForStaff();
            }            
        }

        // if ($data['permitOverallAttendanceSummary']) {
        //     $data['permitCTAttendanceSummary'] = -1;
        // }

        $data['enforce_reading'] = array();
        if ($data['permitFlashNews']) {
            $data['flash_news'] = $this->flash_model->getAllFlashNewsForStaff();
            $data['enforce_reading'] = $this->_flashNewsForEnforceReading('staff');
        }
        // echo '<pre>';print_r($data);die();

        if ($data['permitOverallAttendanceSummary']) {
            $attendanceData = $this->__constructAttendanceSummary();
            $data = array_merge($data, $attendanceData);
        }

        if ($data['permitCTAttendanceSummary'] > 0) {
            $attendanceData = $this->__constructCTAttendanceSummary($data['permitCTAttendanceSummary']);
            $data = array_merge($data, $attendanceData);
        }
       
        if ($data['permitCTAttendanceSummaryV2']) {
            $attendanceDatav2 = $this->__constructAttendanceSummaryV2(0); // Pass 0 to get all assigned sections
            // echo "<pre>"; print_r($attendanceDatav2); die();
            $data = array_merge($data, $attendanceDatav2);
        }
        if($data['permit_gallery']){
            $gallery_for_widget = $this->gallery_model->get_gallery_for_widget();
            foreach($gallery_for_widget as $key => &$val){
                if (isset($val->image_name))
                    $val->image_url= $this->filemanager->getFilePath($val->image_name);
            }
            $data["gallery_for_widget"] = $gallery_for_widget;
            $data["gallery_images_count"] = $this->gallery_model->get_gallery_images_count();
        }

        if ($data['permitBirthdayView']) {
            $birthDayData = $this->__constructBirthdayList();
            $data = array_merge($data, $birthDayData);
        }

        if ($data['dailyPlannerAndCalendar']) {
            $dailyEventsData = $this->__constructDailyEvents();
            $data = array_merge($data, $dailyEventsData);
        }
        // echo "<pre>";print_r($data['dailyEvents']);die();
        if ($data['permitCalendarView']) {
            $data['eventsToday'] = $this->dashboard_model->get_todaysEvents();
            $data['eventsUpcoming'] = $this->dashboard_model->get_upcomingEvents();
            $data['allEvents'] = $this->dashboard_model->getAllEvents();
        }

        if ($data['permitStaffLeaveView']) {
            $data['staffLeaves'] = $this->dashboard_model->getStaffLeavenew_ByDate();
            // $data['staffLeaves'] = $this->dashboard_model->getStaffLeaves();
        }

        if ($data['permitFee']) {
            $data['admissionStatus'] = $this->settings->getSetting('admission_status');
            $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
        }
        if ($data['permitCirculars']) {
            $data['circular_desktop'] = $this->dashboard_model->circular_data_desktop_for_staff($staffId);
        }

        if ($data['permitCircularsV2']) {
            $data['circularv2_desktop'] = $this->dashboard_model->getCircularsAndEmails($staffId, 4);
        }

        // if ($data['student_count_widget']) {
        //     $data['student_count'] = $this->dashboard_model->get_student_count_adm_status_wise();
        //     $data['student_deactivate_count'] = $this->dashboard_model->get_student_count_partial_deactivate();
        // }

        if ($data['parent_ticketing_widget']) {
            $data['parent_ticketing_count'] = $this->dashboard_model->get_ticket_summary_count();
        }

        if ($data['internal_ticketing_widget']) {
            $data['ticketing_count'] = $this->dashboard_model->get_internal_ticket_summary_count();
        }

        // if ($data['ticketing_widget2']) {
        //     $data['input_tickets'] = $this->fees_student_model->get_blueprints();
        // }

        if ($data['student_counselling']) {
            $data['counselling'] = $this->dashboard_model->get_counselling_statistics();
        }

        if ($data['student_nc_statistics']) {
            $data['nc_statistics'] = $this->dashboard_model->get_nc_statistics();
        }

        $data['enquiry_stat'] = array();
        if ($data['enquiry_stat_widget']) {
            $data['enquiry_stat'] = $this->dashboard_model->get_enquiry_statistics();
        }

        if ($data['admission_statistics_widget']) {
            $data['admission_statistics'] = $this->dashboard_model->get_admission_statistics();
        }
        if ($data['substitution_statistics']) {
            $data['substitution_statistics'] = $this->dashboard_model->get_substitution_statistics();
        }

        if ($data['task_basket']) {
            $data['board_details'] = $this->dashboard_model->getAllBoards();
            // $data['task_basket'] = $this->dashboard_model->get_task_basket_data();
        }
        if ($data['permitTodo']) {
            $today = date('Y-m-d');
            $data['todaysTasks'] = $this->taskstodo_model->getTasksByDay($today, null, $staffId);
            //echo "<pre>"; print_r($data['todaysTasks']); die();
            $tomorrow = date('Y-m-d', strtotime($today . ' +1 day'));
            $data['upcomingTasks'] = $this->taskstodo_model->getTasksByTomarrow($tomorrow, $staffId);
        }

        $data['staffId'] = $this->authorization->getAvatarStakeHolderId();
        $staffCache = new stdClass();
        $staffCache->gender = 'M';
        $staffCache->picture_url = '';
        $staffCache->staffId = 0;
        if ($data['staffId'] != 0) {
            // $data['staffData'] = $this->dashboard_model->getStaffData($data['staffId']);
            $staffCache = $this->avatar->makeStaffCache($this->authorization->getAvatarId());
        }
        if ($data['showStaffCount']) {
            $data['staffTypeCount'] = $this->dashboard_model->getStaffCount();
        }
        $this->staffcache->setStaffCache($staffCache);
        $staffData = $this->staffcache->getStaffCache();
        // echo "<pre>"; print_r($data['fees_amount']); die();
        // echo "<pre>"; print_r($data['student_deactivate_count']); die();

        if($data['inventory_statistics'] || $data['category_stock_details_widget']) {
            $data['salesYear']= $this->dashboard_model->get_sales_year();
            $activeSalesYear= $this->dashboard_model->get_activeSalesYear();
            $data['activeSalesYear']= $activeSalesYear;
        }
        if($data['inventory_statistics']) {
            $data['inventoryStatistics']=  $this->dashboard_model->getInventoryStatistics($activeSalesYear);
        }
        if($data['category_stock_details_widget']) {
            $data['categoryStockDetails']=  $this->dashboard_model->categoryStockDetails($activeSalesYear);
        }

        $dashboard = 'dashboard/staff/_desktop_index';
        // $dashboard = 'dashboard/staff/_temp_desktop';
        $data['dashboard_order'] = isset($_SESSION['avatar']->dashboard_order) ? $_SESSION['avatar']->dashboard_order : null;

        $data['main_content']    = $dashboard;
        $this->load->view('inc/template', $data);
    }

    public function categoryStockDetails() {
        $activeSalesYear= $this->input->post('sales_year_id');
        echo json_encode( $this->dashboard_model->categoryStockDetails($activeSalesYear, true)); // force refresh = true
    }

    public function onchange_inventory_statistics() {
        $activeSalesYear= $this->input->post('sales_year_id');
        echo json_encode( $this->dashboard_model->getInventoryStatistics($activeSalesYear, true)); // force refresh = true
    }

    private function __showMobileStaffDashboard(){
        $staffId = $this->authorization->getAvatarStakeHolderId();

        //Load username for the mobile client to parse and know (required for notifications)
        $data['username'] = $this->authorization->getUsername();

        $data['permitFlashNews'] = $this->authorization->isModuleEnabled('FLASH_NEWS');
        $data['permitOnlineClass'] = $this->authorization->isAuthorized('ONLINE_CLASS.MODULE');
        $data['permitAttendanceTake'] = $this->authorization->isModuleEnabled('STUDENT_ATTENDANCE') && $this->authorization->isAuthorized('STUDENT_ATTENDANCE.TAKE');
        $data['permitBirthday'] = $this->authorization->isAuthorized('UPCOMING_BIRTHDAY.VIEW');
        $data['permitTodos'] = $this->authorization->isModuleEnabled('TASKSTODO') && $this->authorization->isAuthorized('TODO.MODULE');
        $data['permitCalendar'] = $this->authorization->isAuthorized('SCHOOL_CALENDAR.MODULE');
        $data['permitCircular'] = $this->settings->isModuleEnabled('CIRCULAR') && $this->authorization->isAuthorized('CIRCULAR.STAFF_CIRCULAR');
        $data['permitStaffLogs'] = $this->authorization->isAuthorized('STAFF.LOGS');
        $data['permitRoomTT'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.VIEW_ROOM_TT');
        $data['permitSectionTT'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_VIEW');
        // $data['permitMyTimetable'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE');
        $data['permitMyTimetablev2'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE');
        $data['permitStaffLeave'] = $this->settings->isModuleEnabled('LEAVE') && $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY');
        $data['permitStudentObservation'] = $this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW');
        $data['permitStaffPayslip'] = $this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.MODULE') && $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS');
        $data['permitStaffExpense'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.MODULE');
        $data['permitStaffObservation'] = $this->authorization->isAuthorized('STAFF_OBSERVATION.MODULE');
        $data['permitHomework'] = $this->authorization->isModuleEnabled('HOMEWORK') && $this->authorization->isAuthorized('HOMEWORK.MODULE');
        $data['permitRoomBooking'] = $this->authorization->isAuthorized('ROOM_BOOKING') && $this->authorization->isAuthorized('ROOM_BOOKING.ALLOW_ROOM_BOOKING');
        $data['permitVisitorRegister'] = $this->authorization->isModuleEnabled('VISITOR') && $this->authorization->isAuthorized('VISITOR.MODULE');
        $data['permitVisitorView'] = $this->authorization->isModuleEnabled('VISITOR') && $this->authorization->isAuthorized('VISITOR.VIEW');
        $data['permitLibraryView'] = $this->authorization->isModuleEnabled('LIBRARY') && $this->authorization->isAuthorized('LIBRARY.STAFF_VIEW');
        //$data['permitPayrollView'] = $this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS');
        $data['permitGalleryView'] = $this->authorization->isModuleEnabled('GALLERY');
        $data['permitSMS'] = $this->authorization->isModuleEnabled('SMS') && $this->authorization->isAuthorized('SMS.SHOW_STAFF_SMS');
        $data['permitTextView'] = $this->authorization->isModuleEnabled('TEXTING');
        $data['permitSMSSend'] = $this->authorization->isModuleEnabled('SMS') && $this->authorization->isAuthorized('SMS.SEND_SMS');
        $data['permitCircularNew'] = $this->settings->isModuleEnabled('CIRCULARS_V2');
        $data['permit_circular_email'] = $this->authorization->isAuthorized('CIRCULARV2.CREATE') && $this->authorization->isModuleEnabled('CIRCULARS_V2');
        $data['permit_parent_ticketing'] = $this->authorization->isAuthorized('PARENT_TICKETING.MODULE') && $this->authorization->isModuleEnabled('PARENT_TICKETING');
        $data['permit_send_text'] = $this->authorization->isAuthorized('TEXTING.SEND') && $this->authorization->isModuleEnabled('TEXTING');
        $data['permit_events'] = $this->authorization->isAuthorized('EVENT.MODULE') && $this->authorization->isModuleEnabled('EVENT');
        $data['permit_msm'] = $this->authorization->isAuthorized('MSM.MODULE') && $this->authorization->isModuleEnabled('MSM');
        $data['permit_task_basket'] = $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_TASKS') && $this->authorization->isModuleEnabled('STAFF_TASKS_BASKET') && $this->authorization->isAuthorized('WIDGET.TASK_BASKET');
        $data['staff_transport'] = $this->authorization->isModuleEnabled('STAFF_TRANSPORT');

        //    echo '<pre>';print_r($data);die();
        if ($data['permitSMS']) {
            $data['sms'] = $this->dashboard_model->getSMSInfo($staffId);
        }

        $data['staffData'] = $this->dashboard_model->getStaffProfileData($staffId);

        if ($data['permitTextView']) {
            $data['texts'] = $this->dashboard_model->getTextInfo($staffId);
        }
        
        if ($data['permitCircular']) {
            $data['circular'] = $this->dashboard_model->getCircularInfo($staffId);
        }

        if ($data['permitCircularNew']) {
            $data['circular_new'] = $this->dashboard_model->getNewCircularInfo($staffId);
        }

        $data['enforce_reading'] = array();
        if ($data['permitFlashNews']) {
            $data['flash_news'] = $this->flash_model->getAllFlashNewsForStaff();
            $data['enforce_reading'] = $this->_flashNewsForEnforceReading('staff');
        }

        if ($data['permitCalendar']) {
            $data['eventsToday'] = $this->dashboard_model->get_todaysEvents();
            $data['eventsUpcoming'] = $this->dashboard_model->get_upcomingEvents();
            $data['allEvents'] = $this->dashboard_model->getAllEvents();
        }

        if ($data['permitTodos']) {
            $today = date('Y-m-d');
            $data['todaysTasks'] = $this->taskstodo_model->getTasksByDay($today, null, $staffId);
            //echo "<pre>"; print_r($data['todaysTasks']); die();
            $tomorrow = date('Y-m-d', strtotime($today . ' +1 day'));
            $data['upcomingTasks'] = $this->taskstodo_model->getTasksByTomarrow($tomorrow, $staffId);
        }

        if ($data['permit_task_basket']) {
            $data['board_details'] = $this->dashboard_model->getAllBoards();
            $data['no_open_tasks'] = $this->dashboard_model->getSTBOpenTasks($staffId);
        }

        $data['staffId'] = $staffId;
        $dashboard = 'dashboard/staff/_mobile_index';
        $data['main_content']    = $dashboard;
        $this->load->view('inc/template', $data);
    }

    private function isStaffAttendanceCheckInDisabled($checkinType){
      if($checkinType=="location_checkin" || $checkinType=="face_checkin"){
        $locationAndFaceCheckin="location_checkin_and_face_checkin";
        // Steps
        // 1. If the user has no checkin priviledge, if not then return 1 -> Disabled
        // 2. If any one of the config namely->staff_attendance_mode and staff_attendance_supported_platforms is disabled then return 1 -> Disabled
        // 3. Else 
          //    Get all the supported platform, and check if the current platform is not found then, return 1
          //    else if platform is found, then check if the given attendance type => 'location_check_in' or 'face_checkin' is found or not, if not then return 1 -> Disabled
          //    else return 0 'Not disabled' i.e they can checkin the staff attendance

        if($checkinType=="face_checkin"){
          $checkInPermissiom = (int)$this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_FACE_CHECKIN');
        }else {
          $checkInPermissiom = (int)$this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_LOCATION_CHECKIN');
        }
        
        if($checkInPermissiom==0) return 1; //1-> Disable checkin/checkout

        if((int)json_decode($this->settings->getSetting("staff_attendance_supported_platforms")) == 0) return 1;

        $staffAttendanceMode=$this->settings->getSetting("staff_attendance_mode");

        if ($staffAttendanceMode != $checkinType && $staffAttendanceMode != $locationAndFaceCheckin) {
          return 1;
        }

        $staffAttendanceSupportedPlatforms = $this->settings->getSetting("staff_attendance_supported_platforms");
        $isViewportEnabled=0;
      

        if($checkinType=="face_checkin"){
          // for checkin we have to only check the mobile viewport
          // checking respective viewports
          if ($this->mobile_detect->isMobile()) {
            // is mobile supported
            $isViewportEnabled = in_array("mobile_browser", json_decode($staffAttendanceSupportedPlatforms)) || in_array("mobile_app", json_decode($staffAttendanceSupportedPlatforms));
          }
        }else{
          if ($this->mobile_detect->isTablet()) {
          // considering tablet in mobile category
          // is mobile supported
          $isViewportEnabled = in_array("mobile_browser", json_decode($staffAttendanceSupportedPlatforms)) || in_array("mobile_app", json_decode($staffAttendanceSupportedPlatforms));
          } else if ($this->mobile_detect->isMobile()) {
            // is mobile supported
            $isViewportEnabled = in_array("mobile_browser", json_decode($staffAttendanceSupportedPlatforms)) || in_array("mobile_app", json_decode($staffAttendanceSupportedPlatforms));
          } else {
            $isViewportEnabled = in_array("desktop", json_decode($staffAttendanceSupportedPlatforms));
          }
        }

        if($isViewportEnabled==0) return 1;

        return 0; // staff attendance check in is enabled
      } else {
        return 1;
      }
    }

    public function staff_mobile_dashboard(){
        $staffId = $this->authorization->getAvatarStakeHolderId();

        //Load username for the mobile client to parse and know (required for notifications)
        $data['username'] = $this->authorization->getUsername();

        $data['permitFlashNews'] = $this->authorization->isModuleEnabled('FLASH_NEWS');
        $data['permitOnlineClass'] = $this->authorization->isAuthorized('ONLINE_CLASS.MODULE');
        $data['permitAttendanceTake'] = $this->authorization->isModuleEnabled('STUDENT_ATTENDANCE') && $this->authorization->isAuthorized('STUDENT_ATTENDANCE.TAKE');
        $data['permitBirthday'] = $this->authorization->isAuthorized('UPCOMING_BIRTHDAY.VIEW');
        $data['permitTodos'] = $this->authorization->isModuleEnabled('TASKSTODO') && $this->authorization->isAuthorized('TODO.MODULE');
        $data['permitCalendar'] = $this->authorization->isAuthorized('SCHOOL_CALENDAR.MODULE');
        $data['permitCircular'] = $this->settings->isModuleEnabled('CIRCULAR') && $this->authorization->isAuthorized('CIRCULAR.STAFF_CIRCULAR');
        $data['permitStaffLogs'] = $this->authorization->isAuthorized('STAFF.LOGS');
        $data['permitRoomTT'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.VIEW_ROOM_TT');
        $data['permitSectionTT'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.SECTION_TIMETABLE_VIEW');
        // $data['permitMyTimetable'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE');
        $data['permitMyTimetablev2'] = $this->authorization->isModuleEnabled('TIMETABLE') && $this->authorization->isAuthorized('TIMETABLE.MY_TIMETABLE');
        $data['permitDailyPlannerAndScheduler'] = $this->authorization->isModuleEnabled('DAILY_PLANNER') && $this->authorization->isAuthorized('DAILY_PLANNER.DAILY_PLANNER_CALENDAR') && $this->authorization->isAuthorized('WIDGET.DAILY_PLANNER');
        $data['permitStaffLeave'] = $this->settings->isModuleEnabled('LEAVE') && $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_APPLY');
        $data['permitStudentObservation'] = $this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW');
        $data['permitStaffPayslip'] = $this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.MODULE') && $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS');
        $data['permitStaffExpense'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.MODULE');
        $data['permitStaffObservation'] = $this->authorization->isAuthorized('STAFF_OBSERVATION.MODULE');
        $data['permitHomework'] = $this->authorization->isModuleEnabled('HOMEWORK') && $this->authorization->isAuthorized('HOMEWORK.MODULE');
        $data['permitRoomBooking'] = $this->authorization->isAuthorized('ROOM_BOOKING') && $this->authorization->isAuthorized('ROOM_BOOKING.ALLOW_ROOM_BOOKING');
        $data['permitVisitorRegister'] = $this->authorization->isModuleEnabled('VISITOR') && $this->authorization->isAuthorized('VISITOR.MODULE');
        $data['permitVisitorView'] = $this->authorization->isModuleEnabled('VISITOR') && $this->authorization->isAuthorized('VISITOR.VIEW');
        $data['permitLibraryView'] = $this->authorization->isModuleEnabled('LIBRARY') && $this->authorization->isAuthorized('LIBRARY.STAFF_VIEW');
        //$data['permitPayrollView'] = $this->authorization->isModuleEnabled('PAYROLL') && $this->authorization->isAuthorized('PAYROLL.VIEW_MY_PAYSLIPS');
        $data['permitGalleryView'] = $this->authorization->isModuleEnabled('GALLERY');
        $data['permitSMS'] = $this->authorization->isModuleEnabled('SMS') && $this->authorization->isAuthorized('SMS.SHOW_STAFF_SMS');
        $data['permitTextView'] = $this->authorization->isModuleEnabled('TEXTING');
        $data['permitSMSSend'] = $this->authorization->isModuleEnabled('SMS') && $this->authorization->isAuthorized('SMS.SEND_SMS');
        $data['permitCircularNew'] = $this->settings->isModuleEnabled('CIRCULARS_V2');
        $data['permit_circular_email'] = $this->authorization->isAuthorized('CIRCULARV2.CREATE') && $this->authorization->isModuleEnabled('CIRCULARS_V2');
        $data['permit_parent_ticketing'] = $this->authorization->isAuthorized('PARENT_TICKETING.MODULE') && $this->authorization->isModuleEnabled('PARENT_TICKETING');
        $data['permit_send_text'] = $this->authorization->isAuthorized('TEXTING.SEND') && $this->authorization->isModuleEnabled('TEXTING');
        $data['permit_events'] = $this->authorization->isAuthorized('EVENT.MODULE') && $this->authorization->isModuleEnabled('EVENT');
        $data['permit_msm'] = $this->authorization->isAuthorized('MSM.MODULE') && $this->authorization->isModuleEnabled('MSM');
        $data['permit_task_basket'] = $this->authorization->isAuthorized('STAFF_TASKS_BASKET.MY_TASKS') && $this->authorization->isModuleEnabled('STAFF_TASKS_BASKET') && $this->authorization->isAuthorized('WIDGET.TASK_BASKET');
        $data['staff_transport'] = $this->authorization->isModuleEnabled('STAFF_TRANSPORT');
        $data['simulate_504_timeout'] = $this->authorization->isModuleEnabled('SIMULATE_504_TIMEOUT');





$data['permit_exam_module'] = $this->authorization->isModuleEnabled('EXAMINATION') && $this->authorization->isAuthorized('EXAMINATION.MODULE');



        if($data['permitDailyPlannerAndScheduler']){
            $data['currentDayEvents'] = $this->dashboard_model->getCurrentDayEvents();
        }

        //    echo '<pre>';print_r($data);die();
        if ($data['permitSMS']) {
            $data['sms'] = $this->dashboard_model->getSMSInfo($staffId);
        }

        $data['staffData'] = $this->dashboard_model->getStaffProfileData($staffId);

        if ($data['permitTextView']) {
            $data['texts'] = $this->dashboard_model->getTextInfo($staffId);
        }
        if ($data['permit_task_basket']) {
            $data['board_details'] = $this->dashboard_model->getAllBoards();
            $data['no_open_tasks'] = $this->dashboard_model->getSTBOpenTasks($staffId);
        }

        if ($data['permitFlashNews'] && $this->settings->getSetting('birthday_flashnews_message')) {
            $data['birthday_news_staff'] = $this->flash_model->getBirthdayOfStaff();
        }

        if ($data['permitCircular']) {
            $data['circular'] = $this->dashboard_model->getCircularInfo($staffId);
        }

        if ($data['permitCircularNew']) {
            $data['circular_new'] = $this->dashboard_model->getNewCircularInfo($staffId);
        }
        if($staffId != 0){
            $data['staff_locked_unlocked_profile_status']= $this->dashboard_model->get_staff_locked_unlocked_profile_status($staffId);
        }

        $data['enforce_reading'] = array();
        if ($data['permitFlashNews']) {
            $data['flash_news'] = $this->flash_model->getAllFlashNewsForStaff();
            $data['enforce_reading'] = $this->_flashNewsForEnforceReading('staff');
        }

        if ($data['permitCalendar']) {
            $data['eventsToday'] = $this->dashboard_model->get_todaysEvents();
            $data['eventsUpcoming'] = $this->dashboard_model->get_upcomingEvents();
            $data['allEvents'] = $this->dashboard_model->getAllEvents();
        }

        if ($data['permitTodos']) {
            $today = date('Y-m-d');
            $data['todaysTasks'] = $this->taskstodo_model->getTasksByDay($today, null, $staffId);
            //echo "<pre>"; print_r($data['todaysTasks']); die();
            $tomorrow = date('Y-m-d', strtotime($today . ' +1 day'));
            $data['upcomingTasks'] = $this->taskstodo_model->getTasksByTomarrow($tomorrow, $staffId);
        }

        // check whether the user can login or not
        $attendanceType=trim($this->settings->getSetting("staff_attendance_mode")," ");
        
        // echo $attendanceType; die();
            // trigger_error('att type '. $attendanceType);

        if($attendanceType=="location_checkin_and_face_checkin"){
            $faceCheckInPermission = (int)$this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_FACE_CHECKIN');
            $locationCheckInPermission = (int) $this->authorization->isAuthorized('STAFF_ATTENDANCE.STAFF_ATTENDANCE_USE_LOCATION_CHECKIN');

            if(($faceCheckInPermission && $locationCheckInPermission) || $faceCheckInPermission){
                $attendanceType = "face_checkin";
            }else {
                $attendanceType = "location_checkin";
            }
        }

        // trigger_error('later type ' . $attendanceType);
        
        
        $data['checkin_disabled'] = $this->isStaffAttendanceCheckInDisabled($attendanceType);
        // trigger_error('checkin_disabled ' . $data['checkin_disabled']);

        $data['staffId'] = $staffId;
        $response = $this->load->view('dashboard/staff/_mobile_index', $data, true);
        echo $response;
    }

    private function __showStudentDashboard()
    {
        $dashboard = 'dashboard/dashboard_student';
        $data['main_content']    = $dashboard;
        $this->load->view('inc/template', $data);
    }

    private function __showMobileStaffDashboardSkeleton() {
        $data['url'] = site_url('dashboard/staff_mobile_dashboard');
        $data['main_content'] = 'dashboard/dashboard_skeleton_mobile.php';
        $data['dashboard'] = true;
        $this->load->view('inc/template', $data);
    }

    private function __showDesktopParentDashboard()
    {
        $isforceChangePasswordForParentsEnabled = $this->settings->getSetting("enable_force_change_password_for_parents");
        if($isforceChangePasswordForParentsEnabled){
            $is_reset_password_required=$this->parent_model->check_parent_reset_password_required();
            if($is_reset_password_required->reset_password_required==1){
                redirect('auth/change_password');
            }
        }

        $admissionFlowV2 = $this->settings->getSetting('enable_parent_side_pending_admission_flow');
        if($admissionFlowV2 == 1){
            redirect('admissionflowv2/index');
        }

        $data['showTask'] = $this->settings->isParentModuleEnabled('STUDENT_TASKS');
        $data['showSMS'] = $this->settings->isParentModuleEnabled('SMS');
        $data['showTx'] = $this->settings->isParentModuleEnabled('TRANSPORT');
        $data['showCircularsNew'] = $this->settings->isParentModuleEnabled('CIRCULARS_V2');
        // $data['showTimetables'] = $this->settings->isParentModuleEnabled('TIMETABLE');
        $data['showAssessments'] = $this->settings->isParentModuleEnabled('ASSESSMENTS');
        $data['showMarksCard'] = $this->settings->isParentModuleEnabled('MARKS_CARD');
        $data['showParentInitiative'] = $this->settings->isParentModuleEnabled('PARENT_INITIATIVE');
        $data['showAttSummary'] = $this->settings->isParentModuleEnabled('ATTENDANCE_SUMMARY');
        $data['showCalendar'] = $this->settings->isParentModuleEnabled('SCHOOL_CALENDAR');
        $data['showFlashNews'] = $this->settings->isParentModuleEnabled('FLASH_NEWS');
        $data['showHomework'] = $this->settings->isParentModuleEnabled('HOMEWORK');
        $data['showVideo'] = $this->settings->isParentModuleEnabled('VIDEOCHAT');
        $data['showTask'] = $this->settings->isParentModuleEnabled('STUDENT_TASKS');
        $data['showSMSCount'] = $this->settings->isParentModuleEnabled('TEXTING');
        $data['showFee'] = $this->settings->isParentModuleEnabled('FEES_DASHBOARD_WIDGET');
        $data['showOtherLinks'] = $this->settings->isParentModuleEnabled('OTHERLINKS');
        $data['permit_gallery'] =  $this->settings->isParentModuleEnabled('GALLERY');
        $data['student_health'] =  $this->settings->isParentModuleEnabled('STUDENT_HEALTH');
        $data['consent_form'] =  $this->settings->isParentModuleEnabled('CONSENT_FORM');
        $studentId   = $this->parent_model->getStudentIdOfLoggedInParent();
        // $previousYears = $this->previous_year_model->get_student_previous_yearsId($studentId);
        // $this->session->set_userdata('acad_year_count',count($previousYears));
        $studentData = $this->parent_model->getStudentDataById($studentId);
        $parentId = $this->authorization->getAvatarStakeHolderId();
        $data['studentData'] = $studentData;
        $user_data = $this->session->userdata('avatar');
        $user_data->fName = $data['studentData']->stdName;
        $this->session->set_userdata('avatar', $user_data);
        $data['csid'] = $studentData->sectionId;

        if ($data['showCircularsNew']) $data['circular_desktop_new'] = $this->dashboard_model->circular_data_desktop_new($parentId);
        if ($data['showCalendar']) $data['upcoming_holidays'] = $this->dashboard_model->get_upcoming_holidays();
        
        if ($data['showAttSummary']) {
            $this->__loadAttendanceData_parentdesktop($studentData, $studentId);
        }

        if($data['permit_gallery']){
            $gallery_for_widget = $this->gallery_model->get_gallery_for_widget();
            foreach($gallery_for_widget as $key => &$val){
                if (isset($val->image_name))
                    $val->image_url = $this->filemanager->getFilePath($val->image_name);
            }
            $data["gallery_for_widget"] = $gallery_for_widget;
        }

        // $data['donot_show'] = $this->parent_model->showBox();
        $data['enforce_reading'] = array();
        if ($data['showFlashNews']) {
            $data['flash_news'] = $this->flash_model->getFlashNewsForSection($studentData->sectionId);
            $data['enforce_reading'] = $this->_flashNewsForEnforceReading('student', $studentData->sectionId);
        }

        if ($data['showHomework']) {
            $data['homework_desktop'] = $this->dashboard_model->homework_data_desktop_for_parent($studentId, $studentData->sectionId);
        }
        $acadYearId= $this->acad_year->getAcadYearId();;
        if ($data['showFee']) {
            $data['student_id'] = $studentId;
            $data['currentAcadyearId']= $acadYearId;
        }
        if($data['showOtherLinks'] ){
            $this->load->model('Otherlinks_model');
            $data['links'] = $this->Otherlinks_model->getEnabledLinksForParents();
        }

        if($data['showAttSummary']){
            $attV2_student_att_summaryData= $this->dashboard_model->get_att_v2_data($studentData, $studentId, $acadYearId);
            $data['att_v2_data'] = $attV2_student_att_summaryData;
        }
        $this->dashboard_model->update_last_access_date();
        $dashboard = 'dashboard/parent/index_desktop';
        $data['main_content']    = $dashboard;
        // echo "<pre>"; print_r($data); die();
        $this->load->view('inc/template', $data);
    }

    private function __loadAttendanceData_parentdesktop($studentData, $studentId)
    {
        $attendance = $this->session->userdata('present_days');
        $lateComerDetails = $this->session->userdata('late_coming_days');
        $working_days = $this->session->userdata('working_days');
        if (empty($attendance)) {
            $attendance = $this->parent_model->getAttendanceSessions($studentData->classId, $studentData->sectionId, 2018, $studentId);
            $lateComerDetails = $this->parent_model->getLateComerDetails($studentId, 2018);
            if (empty($attendance)) {
                // $this->session->set_flashdata('flashWarning', 'No record found for the selection');
                // redirect('parent_controller/attendance');
            }
            $attendance_data = $this->prepareAttendanceData($attendance);
            $data['late_coming_days'] = count($lateComerDetails);
            $present_days = 0;
            foreach ($attendance_data['day'] as $key => $day_value) {
                $day_count = 0;
                foreach ($day_value as $value) {
                    if ($value['status'] == 1) {
                        $day_count++;
                    }
                }
                if ($day_count != 0)
                    $present_days += $day_count / (count($day_value));
            }
            $present_days = number_format($present_days, 1);
            $working_days = count($attendance_data['day']);
            $this->session->set_userdata('present_days', $present_days);
            $this->session->set_userdata('late_coming_days', $data['late_coming_days']);
            $this->session->set_userdata('working_days', $working_days);
        }
    }

    private function _flashNewsForEnforceReading($type, $sectionId = 0) {
        $sectionId = 0;
        $enforcing = $this->flash_model->getEnforceReadingFlashNews($type, $sectionId);
        $news = array();
        if(!empty($enforcing)) {
            if($this->session->userdata('enforce_reading')) {
                $current_id = $this->session->get_userdata()['enforce_reading'];
                if($current_id != $enforcing['id']) {
                    $this->session->set_userdata('enforce_reading', $enforcing['id']);
                    $news = $enforcing;
                }
            } else {
                $this->session->set_userdata('enforce_reading', $enforcing['id']);
                $news = $enforcing;
            }
        }
        return $news;
    }

    public function prepareAttendanceData($old_arr)
    {

        $arr = [];
        $short_name = [];

        foreach ($old_arr as $key => $item) {
            $arr[$item['day']][$key] = $item;
            $short_name[$item['short_name']] = $item['short_name'];
        }

        //ksort($arr, SORT_NUMERIC);
        return ['day' => $arr, 'short_name' => $short_name];
    }


    public function birthdays()
    {
        // $data['stdJustCelebratedDob'] = $this->dashboard_model->get_studentDataforJustCelebratedBirthdayList();
        $data['stdJustCelebratedDob'] = $this->dashboard_model->get_studentWiseDataforJustCelebratedBirthdayList('view_more');
        $data['stdTodayDob'] = $this->dashboard_model->get_studentDataforTodayBirthdayList();
        $data['stdTomorrowDob'] = $this->dashboard_model->get_studentDataforTomorrowBirthdayList();
        $data['stdUpDob'] = $this->dashboard_model->get_studentDataforUpBirthdayList();
        // $data['staffJustCelebratedDob'] = $this->dashboard_model->get_staffJustCelebratedBirthdayList();
        $data['staffJustCelebratedDob'] = $this->dashboard_model->get_staffWiseJustCelebratedBirthdayList('view_more');
        $data['staffTodayDob'] = $this->dashboard_model->get_staffTodayBirthdayList();
        $data['staffTomorrowDob'] = $this->dashboard_model->get_staffDataforTomorrowBirthdayList();
        $data['staffUpDob'] = $this->dashboard_model->get_staffDataforUpBirthdayList();
        $data['main_content']    = 'dashboard/birthdays';
        $this->load->view('inc/template', $data);
    }

    public function dontShowBox()
    {
        echo $this->dashboard_model->dontShow();
    }

    public function parent_no_avatar(){
        $data['main_content']    = 'dashboard/parent/no_avatar';
        $this->load->view('inc/template_error', $data);
    }

    public function more_details(){
        $data['username'] = $this->authorization->getUsername();
        $data['main_content']    = 'sidebar/bottom_nav_bar_menu_staff_mobile';
        $this->load->view('inc/template', $data);
    }

    public function help_support(){
        if ($this->mobile_detect->isMobile()) {
           $data['main_content']    = 'helptext/about_mobile_staff';
        } else {
          $data['main_content']    = 'helptext/about_desktop_staff';
        }
        $this->load->view('inc/template', $data);
    }

    public function notification_test(){
        $data['main_content']    = 'helptext/notification_staff_mobile';
        $this->load->view('inc/template', $data);
    }

    public function submit_notification(){
        $this->load->helper('notification_helper');
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $title = 'Notification';
        $message = 'Notification test';
        $url = site_url('dashboard/notification_test');
        $res = sendStaffNotifications([$staffId], $title, $message, $url);
        $response = $res['response'];
        // trigger_error(json_encode($res));
        $data['status'] = '';
        if($response['status'] == 1) {
            $result = json_decode($response['message']);
            if($result->success == 1) {
                $data['status'] = 'Notification sent successfully';
                $this->session->set_flashdata('flashSuccess', 'Successfully sent');
            } else {
                $data['status'] = 'Failed';
                $this->session->set_flashdata('flashError', 'Sending notification failed');
            }
        } else {
            $data['status'] = 'Failed';
            $this->session->set_flashdata('flashError', 'Sending notification failed');
        }
        redirect('dashboard/notification_test');
    }


    public function other_links() {
        $this->load->model('Otherlinks_model');
        $adminPermission = $this->authorization->isAuthorized('OTHERLINKS.ADMIN');
        if ($adminPermission) {
           $data['links'] = $this->Otherlinks_model->getAllEnabledOtherLinks();
        }else{
            $data['links'] = $this->Otherlinks_model->getEnabledLinksForStaff();
        }
        
        // echo "<pre>"; print_r($data); die();
        $data['main_content'] = 'staff/other_links/index';
        $this->load->view('inc/template', $data);
    }
    
    public function in_active_student(){
        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();
        $data['studentData'] = $this->parent_model->getStudentDataById($studentId);
        $data['main_content'] = 'parent/in_active';
        $this->load->view('inc/template_error', $data);
    }
    
    public function get_fee_summary_details(){
        $bpId = $this->input->post('bpId');
        $student_admission_status = $this->input->post('student_admission_status');
        $result = $this->dashboard_model->get_overall_fee_record($bpId, $student_admission_status);
        echo json_encode($result);
    }

    public function get_fee_summary_details_class_wise(){
        $bpId = $_POST['bpId'];
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->dashboard_model->get_overall_fee_record_class_wise($bpId, $fromDate, $toDate);
        echo json_encode($result);
    }

    public function get_library_trend_details_date_wise(){
        $from_date = $_POST['from_date'];
        $to_date = $_POST['to_date'];
        $result = $this->dashboard_model->get_library_trend_details_date_wise($from_date, $to_date);
        echo json_encode($result);
    }

    public function get_staff_checkin_reporting_wise(){
        $todays_date = $_POST['todays_date'];
        $result = $this->dashboard_model->get_staff_checkin_reporting_wise($todays_date);
        echo json_encode($result);
    }
    
    public function get_staff_leave_trend_details_date_wise(){
        $from_date = $_POST['from_date'];
        $to_date = $_POST['to_date'];
        $result = $this->dashboard_model->get_staff_leave_trend_details_date_wise($from_date, $to_date);
        echo json_encode($result);
    }

    public function get_fee_summary_details_date_wise(){
        $bpId = $_POST['bpId'];
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->dashboard_model->get_overall_fee_record_date_wise($bpId, $fromDate, $toDate);
        echo json_encode($result);
    }

    public function get_ticket_summary(){
        // $bpId = $_POST['bpId'];
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->dashboard_model->get_ticket_summary($fromDate, $toDate);
        echo json_encode($result);
    }

    public function get_internal_ticket_summary(){
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->dashboard_model->get_internal_ticket_summary($fromDate, $toDate);
        echo json_encode($result);
    }

    public function get_visitor_count(){
        // $bpId = $_POST['bpId'];
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->dashboard_model->get_visitor_count($fromDate, $toDate);
        echo json_encode($result);
    }

    public function getLatestStudentTasks() {
        $count = $_POST['count'];
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data = $this->dashboard_model->getLatestStudentTasks($student_id, $count);
        echo json_encode($data);
    }

    public function getStudentCount() {
        echo json_encode($this->dashboard_model->getStudentCount());
    }

    // public function getStaffCount() {
    //     echo json_encode($this->dashboard_model->getStaffCount());
    // }

    public function getCircularCount() {
        $data = $this->dashboard_model->getCircularCount();
        echo json_encode($data);
    }

    public function getSMSCount() {
        $data = $this->dashboard_model->getSMSCount();
        echo json_encode($data);
    }

    public function getReportingStaffData(){
    $data = $this->dashboard_model->getStaffReportingData();
    echo json_encode($data);
  }

    public function getStudentTaskCount() {
        $student_id = $this->parent_model->getStudentIdOfLoggedInParent();
        $data = $this->dashboard_model->getStudentTaskCount($student_id);
        echo json_encode($data);
    }

    public function getTasksByStaff() {
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $data = $this->dashboard_model->getTasksByStaff($staff_id);
        echo json_encode($data);
    }

    public function getTaskSummary() {
        $count = $_POST['count'];
        $data = $this->dashboard_model->getTaskSummary($count);
        echo json_encode($data);
    }

    public function getStaffTaskSummary() {
        $count = $_POST['count'];
        $data = $this->dashboard_model->getStaffTaskSummary($count);
        echo json_encode($data);
    }

    public function getCommunicationSummary() {
        $count = $_POST['count'];
        $mode = $_POST['mode'];
        $data = $this->dashboard_model->getCommunicationSummary($count, $mode);
        echo json_encode($data);
    }

    public function getAttendanceSummary() {
        $date = date('Y-m-d');
        $data['class_section'] = $this->dashboard_model->getClassSection();
        $data['attendance'] = $this->dashboard_model->getAttendanceSummary($date);
        echo json_encode($data);
    }


    public function testing_location() {
        $data['main_content'] = 'dashboard/staff/blocks/test_location';
        $this->load->view('inc/template', $data);
    }

    // staff count 
    public function staff_count() {
        $staff_count= $this->dashboard_model->getStaffCount();
        echo json_encode($staff_count);
    }

    // student count genderwise
    public function student_count_as_gender() {
        $student_count_genderwise= $this->dashboard_model->getStudentCount();
        echo json_encode($student_count_genderwise);
    }

    public function student_tracking_count() {
//     $date= $_POST['date'];
// echo '<pre>'; print_r($date); die();
        $student_tracking_count= $this->dashboard_model->student_tracking_count();
        echo json_encode($student_tracking_count);
    }

    public function student_count_as_admission_status() {
        $student_count_statuswise= $this->dashboard_model->student_count_as_admission_status();
        // echo '<pre>'; print_r($student_count_statuswise); die();
        echo json_encode($student_count_statuswise);
    }

    public function get_enquiry_chart_summary(){
        $fromDate = $_POST['fromDate'];
        $toDate = $_POST['toDate'];
        $result = $this->dashboard_model->enquiry_day_wise_total_count($fromDate, $toDate);
        echo json_encode($result);
    }

    public function student_statistics_count() {
        $student_count_statuswise= $this->dashboard_model->student_statistics_count();
       // echo '<pre>'; print_r($student_count_statuswise); die();
        echo json_encode($student_count_statuswise);
    }

    public function infirmary_statistics_count() {
        $student_count_statuswise= $this->dashboard_model->infirmary_statistics_count();
        echo json_encode($student_count_statuswise);
    }

    public function get_approval_widget_data(){
        $approval_widget_data = $this->dashboard_model->get_approval_widget_data();
        echo json_encode($approval_widget_data);
    }

    public function staff_anniversary_widget_data(){
        $anniversary = $this->dashboard_model->get_staff_data_for_anniversary();
        echo json_encode($anniversary);
    }

    public function get_staff_leaves_category_wise(){
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $result = $this->dashboard_model->get_staff_leave_quota_usage($staffId);
        // echo "<pre>"; print_r($result);die();
        echo json_encode($result);
    }

    // public function get_task_current_month_calendar(){
    //     $result=$this->dashboard_model->get_task_current_month_calendar();
    //     $month  = date('m');
    //     $year   = date('Y');
    //     // echo "<pre>";print_r($result);die();
    //     return json_encode($result);
    // }

    public function getallopenmytasks(){
        $list_id = $this->input->get('list_id');
        $result = $this->dashboard_model->getallopenmytasks($list_id);
        echo json_encode($result);
    }

    public function getalldelegatedtasks(){
        $list_id = $this->input->get('list_id');
        $result = $this->dashboard_model->getalldelegatedtasks($list_id);
        echo json_encode($result);
    }

    public function getallduetasks(){
        $list_id = $this->input->get('list_id');
        $result = $this->dashboard_model->getallduetasks($list_id);
        echo json_encode($result);
    }

    public function getallhighprioritytasks(){
        $list_id = $this->input->get('list_id');
        $result = $this->dashboard_model->getallhighprioritytasks($list_id);
        echo json_encode($result);
    }

    public function get_staff_attendance_current_month_calendar(){
        $result=$this->dashboard_model->get_staff_attendance_current_month_calendar($_POST);
        $month  = date('m');
        $year   = date('Y');
        
        $data = array();
        foreach ($result as $key => $val){
            $text=$val->status;
            $date=$val->created_on;
            
            $color="green";
            if($val->status=="AB") $color="red";
            if($val->status=="HD") $color="blue";

            if($val->is_late==1){
                $text= $text."(L)";
            }
            $data[] = array('title'=>$text,'start'=>$year.'-'.$month.'-'.$date, 'end'=>$year.'-'.$month.'-'.$date,'className'=>$color);
        }
        echo json_encode($data);
    }

    public function get_staff_attendance_checkin_data() {
        $result = $this->dashboard_model->get_staff_attendance_checkin_data();

        echo json_encode($result);
    }
    public function get_visitor_widget_data() {
        $result= $this->dashboard_model->get_visitor_widget_data();
        echo json_encode($result);
    }


    public function save_dashboard_order() {
        $dashboard_order = $_POST['dashboard_order'];
        $avatar_id = $this->authorization->getAvatarId();
        $result = $this->dashboard_model->save_dashboard_order($dashboard_order, $avatar_id);
        //Add to the session
        $_SESSION['avatar']->dashboard_order = $dashboard_order;
        echo json_encode($result);
    }

    public function get_library_statistics_data(){
        $result= $this->dashboard_model->get_library_statistics_data();
        echo json_encode($result);
    }

    public function get_transport_statistics_data(){
        $result= $this->dashboard_model->get_transport_statistics_data();
        echo json_encode($result);
    }

    public function get_ids_from_rfid(){
        $rfid_number = $_POST['rfid_number'];
        $res = $this->dashboard_model->get_ids_from_rfid($rfid_number);
        echo json_encode($res);
    }

    public function get_single_window_weekly_data(){
        $result = $this->dashboard_model->get_single_window_weekly_data();
        echo json_encode($result);
    }

    private function __showMobileParentDashboardSkeleton_v2() {
        $admissionFlowV2 = $this->settings->getSetting('enable_parent_side_pending_admission_flow');
        if($admissionFlowV2 == 1){
            redirect('admissionflowv2/index');
        } else {
            //Check for force reset password
            $isforceChangePasswordForParentsEnabled=$this->settings->getSetting("enable_force_change_password_for_parents");
            if($isforceChangePasswordForParentsEnabled){
                $is_reset_password_required=$this->parent_model->check_parent_reset_password_required();
                if($is_reset_password_required->reset_password_required==1){
                    redirect('auth/change_password');
                }
            }
            
            //Update last access date
            $this->dashboard_model->update_last_access_date();
    
            //Render the dashboard
            $data['url'] = site_url('dashboard/parent_mobile_dashboard');
            $data['main_content'] = 'dashboard/dashboard_skeleton_mobile.php';
            $data['dashboard'] = true;
            $this->load->view('inc/template', $data);
        }
    }

    public function parent_mobile_dashboard() {        
        // Initialize default values and validate student data
        $data = [];
        
        // Get Student Data with validation
        $student_data = $this->parent_model->get_student_data_by_id_v2();
        if (empty($student_data)) {
            // Function to logout and redirect
            js_logout('Some unexpected error occurred. Please re-login and try.');
            // The function will exit, so no code after this will execute if student_data is empty
        }
        $data['student_data'] = $student_data;
        
        $is_approved_student = $data['student_data']->is_approved_student;
        $is_next_year_student = $data['student_data']->is_next_year_student;        
        $data['username'] = $this->authorization->getUsername();

        //The following will be avilable for only current year students
        $data['permit_attendance'] = $this->settings->isParentModuleEnabled('ATTENDANCE') && $is_approved_student && !$is_next_year_student;
        $data['permit_attendance_v2'] = $this->settings->isParentModuleEnabled('ATTENDANCE_V2') && $is_approved_student && !$is_next_year_student;
        $data['permit_timetable'] = $this->settings->isParentModuleEnabled('TIMETABLE') && $is_approved_student && !$is_next_year_student;
        $data['permit_homework'] = $this->settings->isParentModuleEnabled('HOMEWORK') && $is_approved_student && !$is_next_year_student;
        $data['permit_student_tasks'] = $this->settings->isParentModuleEnabled('STUDENT_TASKS') && $is_approved_student && !$is_next_year_student;
        $data['permit_assessment_timetable'] = $this->settings->isParentModuleEnabled('ASSESSMENTS') && $is_approved_student && !$is_next_year_student;
        $data['permit_assessment_portions'] = $this->settings->isParentModuleEnabled('ASSESSMENT_POSRTIONS_V2') && $is_approved_student && !$is_next_year_student;
        $data['permit_assessment_marks_display'] = $this->settings->isParentModuleEnabled('ASSESSMENT_MARKS') && $is_approved_student && !$is_next_year_student;
        $data['permit_assessment_marks_card'] = $this->settings->isParentModuleEnabled('MARKS_CARD') && $is_approved_student && !$is_next_year_student;
        $data['permit_assessment_marks_analysis'] = $this->settings->isParentModuleEnabled('MARKS_ANALYSIS') && $is_approved_student && !$is_next_year_student;
        $data['permit_student_leave'] = $this->settings->isParentModuleEnabled('STUDENT_LEAVE') && $is_approved_student && !$is_next_year_student;
        $data['permit_student_certificates'] = $this->settings->isParentModuleEnabled('CERTIFICATES') && $is_approved_student && !$is_next_year_student;
        $data['permit_afl'] = $this->settings->isParentModuleEnabled('AFL') && $is_approved_student && !$is_next_year_student;
        $data['permit_non_compliance'] = $this->settings->isParentModuleEnabled('NON_COMPLIANCE') && $is_approved_student && !$is_next_year_student;
        $data['permit_escort_auth'] = $this->settings->isParentModuleEnabled('ESCORT_AUTH') && $is_approved_student && !$is_next_year_student;
        $data['permit_library_parent'] = $this->settings->isParentModuleEnabled('LIBRARY_PARENT') && $is_approved_student && !$is_next_year_student;
        $data['permit_event_v2'] = $this->settings->isParentModuleEnabled('EVENT_V2') && $is_approved_student;
        $data['permit_transport'] = $this->settings->isParentModuleEnabled('TRANSPORT') && $is_approved_student && !$is_next_year_student;
        $data['permit_transport_request'] = $this->settings->isParentModuleEnabled('TRANSPORT_REQUEST') && $is_approved_student;
        $data['permit_student_exit_flow'] = $this->settings->isParentModuleEnabled('STUDENT_EXIT_FLOW') && $is_approved_student && !$is_next_year_student;
        $data['permit_online_class_v2'] = $this->settings->isParentModuleEnabled('ONLINE_CLASS_V2') && $is_approved_student && !$is_next_year_student;
        $data['permit_classroom_chronicles'] = $this->settings->isParentModuleEnabled('CLASSROOM_CHRONICLES') && $is_approved_student && !$is_next_year_student;

        //The following will be available for both current and next year's students
        $data['permit_flash_news'] = $this->settings->isParentModuleEnabled('FLASH_NEWS') && $is_approved_student;
        $data['permit_calendar'] = $this->settings->isParentModuleEnabled('SCHOOL_CALENDAR') && $is_approved_student;
        $data['permit_texting'] = $this->settings->isParentModuleEnabled('TEXTING') && $is_approved_student;
        $data['permit_circular'] = $this->settings->isParentModuleEnabled('CIRCULARS_V2') && $is_approved_student;
        $data['permit_student_inventory'] = $this->settings->isParentModuleEnabled('STUDENT_INVENTORY') && $is_approved_student;
        $data['permit_gallery'] = $this->settings->isParentModuleEnabled('GALLERY') && $is_approved_student;
        $data['permit_parent_ticketing'] = $this->settings->isParentModuleEnabled('PARENT_TICKETING') && $is_approved_student;
        $data['permit_other_links'] = $this->settings->isParentModuleEnabled('OTHERLINKS') && $is_approved_student;
        $data['permit_student_wallet'] = $this->settings->isParentModuleEnabled('WALLET') && $is_approved_student;
        $data['permit_upload_aadhaar'] = $this->settings->isParentModuleEnabled('UPLOAD_AADHAR') && $is_approved_student;

        //The following will be permitted for non-approved ie pending students.
        $data['permit_fees_v2'] = $this->settings->isParentModuleEnabled('FEESV2');
        $data['permit_fees_jodo'] = $this->settings->isParentModuleEnabled('FEES_JODO');
        $data['permit_fees_25_26'] = $this->settings->isParentModuleEnabled('FEES_25_26');
        $data['permit_fees_multiple_blueprint'] = $this->settings->isParentModuleEnabled('FEES_MULTIPLE_BLUEPRINT');
        $data['permit_consent_form'] = $this->settings->isParentModuleEnabled('CONSENT_FORM');
        $data['permit_student_health'] = $this->settings->isParentModuleEnabled('STUDENT_HEALTH');
        $data['permit_id_cards'] = $this->settings->isParentModuleEnabled('ID_CARDS');

        //Get Enforced Flash news data
        $data['enforced_flash_news'] = 'Enforced Flash!!!';
        $data['enforce_reading'] = array();

        //Get Flash news data
        $data['is_enforce'] = 0;
        if ($data['permit_flash_news']) {
            $data['flash_news'] = $this->flash_model->get_flash_news_for_section_v2($data['student_data']->class_section_id, $data['student_data']->class_name, $data['student_data']->section_name, $data['student_data']->board);

            //Is there a enforce_reading?
            $data['enforce_reading'] = array();
            foreach ($data['flash_news'] as $flash_obj) {
                if ($flash_obj->enforce_reading == '1') {
                    $data['is_enforce'] = 1;
                    $data['enforce_reading'] = $flash_obj;
                }
            }
        }

        //Get Calendar Data
        if ($data['permit_calendar']) {
            $data['num_today_calendar_events'] = $this->parent_model->get_num_today_calendar_events_v2($data['student_data']->board);
        }

        //Get Texting info
        if($data['permit_texting']) {
            $data['texts'] = $this->parent_model->getTextsInfo($data['student_data']->parent_id);
        }
        
        //Get Circular info
        if($data['permit_circular']) {
            $data['circular_count'] = $this->parent_model->get_circular_unread_count($data['student_data']->parent_id);
        }

        //Get Number of Homeworks
        if($data['permit_homework']) {
            $data['homeworks'] = $this->parent_model->getTodaysHomework($data['student_data']->class_section_id);
        }

        //Get Number of Student tasks
        if($data['permit_student_tasks']) {
            $data['student_task'] = $this->parent_model->getTodaysStudentTasks($data['student_data']->student_id);
        }

        //Get if new gallery is enabled
        if($data['permit_gallery']) {
            $data['new_gallery_available'] = $this->parent_model->checkNewGalleryAvailable();
        }

        if ($this->mobile_detect->isTablet()) {
                $response = $this->load->view('dashboard/parent_v2/index_mobile', $data, true);
        }else{
                $response = $this->load->view('dashboard/parent_v2/index_mobile', $data, true);
            }
            echo $response;
    }

    public function getAllTasksBoardWise(){
        $input = $this->input->post();
        $result = $this->dashboard_model->getAllTasksBoardWise($input['board_id']);
        echo json_encode($result);
    }
}
