<!-- Flash News -->
<div class="page-content-wrap">
  <?php 
  $is_enforce = 0;
  if(!empty($enforce_reading)) {
      $is_enforce = 1;
  }
  if ($permitFlashNews) { ?>
    <div class="col-md-12" style="padding:0px;">
      <?php $this->load->view('dashboard/staff/blocks/_flash_news_block'); ?>
    </div>
  <?php } ?>
</div>
<script src='https://cdn.jsdelivr.net/npm/sweetalert2@11'></script>

<!-- Carousel -->
<!-- <div class="col-md-12" style="padding:0px;">
  <?php //$this->load->view('dashboard/staff/blocks/_carousel'); ?>
</div> -->

<!--- <PERSON><PERSON><PERSON><PERSON>: Removing the widget block and replacing with square widgets --->
<?php // $this->load->view('dashboard/staff/blocks/_widgets_block'); ?>
<?php
  $staff_profile_message_popup = 'Your staff profile needs to be updated and confirmed. Click on the go to profile button to start the process';
  $popup_description = $this->settings->getSetting('staff_profile_confirm_popup_description');
  if ($popup_description) {
      $staff_profile_message_popup = $popup_description;
  }
?>
<?php
  if(! empty($staff_locked_unlocked_profile_status) && $staff_locked_unlocked_profile_status=='unlocked'){
    if (!isset($_COOKIE['popup_shown'])) {
      // Set a cookie to expire in 1 day
      setcookie('popup_shown', '1', time() + 86400, "/");

      echo "<script>
            Swal.fire({
              title: 'Profile Confirmation!',
              text: '$staff_profile_message_popup',
              icon: 'info',
              showCancelButton: true,
              confirmButtonText: 'Go to profile'
            }).then((result) => {
              if (result.isConfirmed) {
                window.location.href = 'staff/Staff_profile_view_controller/staff_profile_view';
              }
          });
      </script> ";
    }
  }
?>
<div class="col-md-12" style="padding-left:8px;padding-right:8px;" >

    <div class="col-md-12 justify-content-end mb-3" id="save_reset_dashboard_buttons" style="display:none">
      <button class="btn btn-editDash-save mr-2" onclick="save_dashboard_order()"> Save and Close </button>
      <button class="btn btn-editDash-close mr-2" onclick="close_without_saving()"> Close without saving </button>
      <button class="btn btn-editDash-reset" onclick="reset_dashboard_order()">Reset to Factory</button>
    </div>

    <div class="edit-box" id="drag_dashboard">

    <!-- Task Basket -->
    <?php //if ($task_basket) { 
        //echo "<div class='px-2' id='task_basket_widget'>";
        //$this->load->view('dashboard/staff/blocks/task_basket_widget_v2.php'); 
        //echo "</div>";
    //} ?>

    <?php if ($task_basket) { 
        echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='task_basket_widget'>";
        $this->load->view('dashboard/staff/blocks/task_basket_widget_v2.php'); 
        echo "</div>";
    } ?>
    
    <!-- DAILY PLANNER -->
    <?php if ($dailyPlannerAndCalendar) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_daily_planner'>";
      $this->load->view('dashboard/staff/blocks/_daily_planner_desktop'); 
      echo "</div>";
    } ?>

    <!-- Student Count Genderwise widget -->
    <?php if ($student_count_widget_by_gender) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_count_by_gender'>";
      $this->load->view('dashboard/staff/blocks/_student_count_by_gender'); 
      echo "</div>";
    } ?>

    <?php if ($internal_ticketing_widget){
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_internal_ticketing_trending_Chart'>";
      $this->load->view('dashboard/staff/blocks/_internal_ticketing_trending_Chart');
      echo "</div>";
    } ?>

    <!-- Student Check-in Tracking -->
    <?php if ($student_checkin_tracking) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_checkin_transaction'>";
      $this->load->view('dashboard/staff/blocks/_student_checkin_transaction'); 
      echo "</div>";
    } ?>

    <!-- Enquiry Count  widget -->
    <?php if ($enquiry_stat_widget) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_enquiry_catg_widget_statistics'>";
      $this->load->view('dashboard/staff/blocks/_enquiry_catg_widget_statistics'); 
      echo "</div>";
    } ?>

    <?php if ($admission_statistics_widget) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id=''>";
      $this->load->view('dashboard/staff/blocks/_admission_catg_widget_statistics'); 
      echo "</div>";
    } ?>

        <!-- Student 360 from RFID -->
        <?php if ($rfid_widget) {
        echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student360_from_rfid'>";
        $this->load->view('dashboard/staff/blocks/_student360_from_rfid'); 
        echo "</div>";
    } ?>

    <!-- staff leaves category wise -->
    <?php if ($permit_staff_leaves_widget) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='staff_leaves_category_wise'>";
      $this->load->view('dashboard/staff/blocks/staff_leaves_category_wise'); 
      echo "</div>";
    } ?>

    <!-- staff Attendance month calender -->
    <?php if ($permit_month_wise_staff_calender_widget) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='staff_attendance_current_month_calendar'>";
      $this->load->view('dashboard/staff/blocks/staff_attendance_current_month_calendar'); 
      echo "</div>";
    } ?>

    <!-- Staff Count widget -->
    <?php if ($staff_catg_widget) { 
        echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_staff_count_widget'>";
        $this->load->view('dashboard/staff/blocks/_staff_count_widget'); 
        echo "</div>";
    } ?>

    <?php if ($showOtherLinks) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_desktop_otherlinks'>";
      $this->load->view('dashboard/staff/blocks/_desktop_otherlinks');  
      echo "</div>";
    } ?>

    <?php if ($parent_ticketing_widget){
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='parent_ticketing_widget'>";
      $this->load->view('dashboard/staff/blocks/_input_ticketing_trending_Chart');
      echo "</div>";
    } ?>



    <?php //if ($student_counselling){
      //echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='student_counselling'>";
      //$this->load->view('dashboard/staff/blocks/_student_counselling_widget');
      //echo "</div>";
    //} ?>

    <?php if ($enquiryWidget){
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_enquiry_trending_Chart'>";
      $this->load->view('dashboard/staff/blocks/_enquiry_trending_Chart');
      echo "</div>";
    } ?> 

    <?php if($permit_gallery) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_gallery'>";
      $this->load->view('dashboard/staff/blocks/_gallery'); 
      echo "</div>";
    } ?>

   <?php if($student_nc) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_nc'>";
      $this->load->view('dashboard/staff/blocks/_student_nc'); 
      echo "</div>";
    } ?>

    <?php if($student_nc_statistics) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_nc_statistics'>";
      $this->load->view('dashboard/staff/blocks/_student_nc_statistics'); 
      echo "</div>";
    } ?>

    <?php if($student_observation) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_observation'>";
      $this->load->view('dashboard/staff/blocks/_student_observation'); 
      echo "</div>";
    } ?>

    <?php if($single_window) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_single_window'>";
      $this->load->view('dashboard/staff/blocks/_single_window'); 
      echo "</div>";
    } ?>

    <?php if($student_councelling_trend) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_councelling_trend'>";
      $this->load->view('dashboard/staff/blocks/_student_councelling_trend'); 
      echo "</div>";
    } ?>

    <?php if($show_communication_data) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_communication_summary'>";
      $this->load->view('dashboard/staff/blocks/_communication_summary'); 
      echo "</div>";
    } ?>

    <!-- Fee View -->
    <?php if ($permitFee) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='fee_summary_table'>";
      $this->load->view('dashboard/staff/blocks/_fee_summary_table');
      echo "</div>";
    } ?>

    <?php if ($permitFee){
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_fee_trending_chart'>";
      $this->load->view('dashboard/staff/blocks/_fee_trending_Chart');
      // $this->load->view('dashboard/staff/blocks/_fee_trending_Chart1');
      echo "</div>";
    } ?> 

    <!-- Substitution View -->
    <?php if ($substitution_statistics) { 
        echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_substitution_widget'>";
        $this->load->view('dashboard/staff/blocks/_substitution_widget'); 
        echo "</div>";
    } ?>

    <!-- Birthday View -->
    <?php if ($permitBirthdayView) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_upcoming_birthday_table'>";
      $this->load->view('dashboard/staff/blocks/_upcoming_birthday_table');
      echo "</div>";
    } ?>

    <!-- Staff attendance check-in widget -->
    <?php 
      if ($permit_staff_attendance_checkin_widget) {
        echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_staff_attendance_checkin_widget'>";
        $this->load->view('dashboard/staff/blocks/_staff_attendance_checkin_widget'); 
        echo "</div>";
      } 
    ?>

    <!-- Overall Attendance Summary -->
    <?php if ($permitOverallAttendanceV2Summary) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='overall_subject_attendance_summary'>";
      $this->load->view('dashboard/staff/blocks/_student_attendance_v2_summary'); 
      echo "</div>";
    } ?>

    <!-- Staff Leave View -->
    <?php if($permitStaffLeaveView) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_staff_leave_table'>";
      $this->load->view('dashboard/staff/blocks/_staff_leave_table');
      echo "</div>";
    } ?> 

    <?php if($show_task_assigned) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_new_task_summary_magnt'>";
      $this->load->view('dashboard/staff/blocks/_new_task_summary_magnt'); 
      echo "</div>";
    } ?>

    <?php if($show_task_summary) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_new_task_summary'>";
      $this->load->view('dashboard/staff/blocks/_new_task_summary'); 
      echo "</div>";
    } ?>

    <!-- Calendar View -->
    <?php if ($permitCalendarView) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_school_calendar_table'>";
      $this->load->view('dashboard/staff/blocks/_school_calendar_table');
      echo "</div>";
    } ?>

    <!-- Staff Circular View -->
    <?php if ($permitCircularsV2) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_staff_circulars_v2'>";
      $this->load->view('dashboard/staff/blocks/_staff_circulars_v2');
      echo "</div>";
    } ?>

    <?php if ($student_widget_statistics) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_student_catg_widget_statistics'>";
      $this->load->view('dashboard/staff/blocks/_student_catg_widget_statistics'); 
      echo "</div>";
    } ?>
    
    <?php if ($infirmary_visitor_widget_data) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='infirmary_visitor_widget_data'>";
      $this->load->view('dashboard/staff/blocks/get_infirmary_visitor_widget_data'); 
      echo "</div>";
    } ?>

    <?php if ($infirmary_statistics) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='infirmary_statistics'>";
      $this->load->view('dashboard/staff/blocks/get_infirmary_statistics'); 
      echo "</div>";
    } ?>

    <?php if ($get_approval_widget_data) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='get_approval_widget_data'>";
      $this->load->view('dashboard/staff/blocks/get_approval_widget_data'); 
      echo "</div>";
    } ?>

    <?php if ($staff_anniversary_widget_data) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='upcoming_staff_anniversary'>";
      $this->load->view('dashboard/staff/blocks/_staff_anniversary_widget'); 
      echo "</div>";
    } ?>

    <!-- Overall Attendance Summary -->
    <?php if ($permitOverallAttendanceSummary) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='overall_day_attendance_summary'>";
      $this->load->view('dashboard/staff/blocks/_student_attendance_summary'); 
      echo "</div>";
    } ?>

     <?php if ($permitOverallAttendanceSummary) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='overall_day_attendance_summary'>";
      $this->load->view('dashboard/staff/blocks/_student_attendance_summary'); 
      echo "</div>";
    } ?>
     <!-- Daily Visitor Widget -->
    <?php if ($permission_visitor_data) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='daily_visitor_data'>";
      $this->load->view('dashboard/staff/blocks/daily_visitor_data'); 
      echo "</div>";
    } ?>


    <!--- Dummy Widget -->
    <?php if ($staff_on_leave_widget) {
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_staff_on_leave_trending_Chart'>";
      $this->load->view('dashboard/staff/blocks/_staff_on_leave_trending_Chart');
      echo "</div>";
    } ?>

    <!--- Dummy Widget -->
    <?php if($show_staff_task_assigned) { 
      // echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='_staff_task_summary'>";
      // $this->load->view('dashboard/staff/blocks/_staff_task_summary'); 
      // echo "</div>";
    } ?>

    <?php if ($library_statistics) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='library_statistics'>";
      $this->load->view('dashboard/staff/blocks/_library_statistics'); 
      echo "</div>";
    } ?>

    <?php if ($transportation_statistics) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='transportation_statistics'>";
      $this->load->view('dashboard/staff/blocks/_transportation_statistics'); 
      echo "</div>";
    } ?>

    <?php if ($inventory_statistics) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='inventory_statistics'>";
      $this->load->view('dashboard/staff/blocks/_inventory_statistics'); 
      echo "</div>";
    } ?>
    
    <?php if ($category_stock_details_widget) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='category_stock_details'>";
      $this->load->view('dashboard/staff/blocks/_inventory_category_stock_details'); 
      echo "</div>";
    } ?>
 
    <!-- Staff Attendance Reports Wise -->
    <?php if ($staff_attendance_reports_wise){
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='staff_attendance_reports_wise'>";
      $this->load->view('dashboard/staff/blocks/_staff_attendance_reporting_wise'); 
      echo "</div>";
    }?>

    <?php if ($books_trend_chart) { 
      echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget' id='books_trend_id'>";
      $this->load->view('dashboard/staff/blocks/_books_issued_received_chart'); 
      echo "</div>";
    } ?>

<!-- <?php //if($permitStaffTT && !$this->authorization->isSuperAdmin()) { 
      // $this->load->view('dashboard/staff/blocks/_staff_weekly_timetable'); 
    //} ?> -->

    <!-- Manjukiran 6/9: Commenting out to reduce queries to database --->
    <!-- <?php //if($permitStaffTT && !$this->authorization->isSuperAdmin()) { 
      // $this->load->view('dashboard/staff/blocks/_staff_weekly_timetable_v2'); 
    //} ?> -->

    <?php //if ($student_catg_widget) { 
      // echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12 px-2 drag_widget'>";
      // $this->load->view('dashboard/staff/blocks/_student_catg_widget'); 
      // echo "</div>";
    //} ?>

    <?php if ($permitFee) {
      // $this->load->view('dashboard/staff/blocks/_fee_summary_tableBarChart');
    } ?>

    <!-- Class Teacher Attendance Summary -->
    <?php //if ($permitCTAttendanceSummary > 0) {
      // $this->load->view('dashboard/staff/blocks/_student_attendance_summary_classteacher');
    //} ?>

    </div>
    <!-- </div> -->
  </div>
</div>

<script type="text/javascript">
  $(document).ready(function (){
    var is_enforce = <?php echo $is_enforce; ?>;
    if(is_enforce) {
        var enforce_reading = JSON.parse(jsonEscape(JSON.stringify(<?php echo json_encode($enforce_reading) ?>)));

        function jsonEscape(str) {
            return str.replace(/\n/g, "\\\\n").replace(/\r/g, "\\\\r").replace(/\t/g, "\\\\t");
        }
        showFlashNews(enforce_reading);
    }
  });

  // Load the dashboard state from localStorage on page load
  loadDashboardState();

  function setup_edit_dashboard() {
    const dashboard = document.querySelector('#drag_dashboard');
    const widgets = document.querySelectorAll('.drag_widget');

    // Set up the drag and drop functionality
    let dragItem = null;

    widgets.forEach(widget => {
      widget.setAttribute('draggable', true);

      widget.addEventListener('mouseenter', () => {
        widget.style.cursor = 'grab';
      });

      // Set the cursor to 'grabbing' when the widget is being dragged
      widget.addEventListener('mousedown', () => {
        widget.style.cursor = 'grabbing';
      });

      // Reset the cursor to 'grab' when the widget is released
      widget.addEventListener('mouseup', () => {
        widget.style.cursor = 'grab';
      });

      widget.addEventListener('dragstart', function() {
        dragItem = widget;
        setTimeout(() => {
          widget.style.display = 'none';
        }, 0);
      });

      widget.addEventListener('dragend', function() {
        setTimeout(() => {
          dragItem.style.display = 'block';
          dragItem = null;
          saveDashboardState();
        }, 0);
      });

      $('#save_reset_dashboard_buttons').show();
      $(".edit-box").addClass("edit-box_active");
      $(".drag_widget").addClass("box-small_active");
      // $(".edit-box").toggleClass("edit-box_active");
      // $(".drag_widget").toggleClass("box-small_active");
    });

    dashboard.addEventListener('dragover', function(event) {
      event.preventDefault();
    });

    dashboard.addEventListener('drop', function(event) {
      event.preventDefault();
      const dropTarget = event.target.closest('.drag_widget');
      if (dropTarget) {
        dashboard.insertBefore(dragItem, dropTarget);
      } else {
        dashboard.appendChild(dragItem);
      }
      saveDashboardState();
    });
  }

  function remove_edit_dashboard() {
    const dashboard = document.querySelector('#drag_dashboard');
    const widgets = document.querySelectorAll('.drag_widget');

    widgets.forEach(widget => {
      widget.setAttribute('draggable', false);
      widget.removeEventListener('dragstart', null);
      widget.removeEventListener('dragend', null);
    });
    dashboard.removeEventListener('dragover', null);
    dashboard.removeEventListener('drop', null);
    $('#save_reset_dashboard_buttons').css('display','none');
    $(".edit-box").removeClass("edit-box_active");
    $(".drag_widget").removeClass("box-small_active");
  }

  // Save the current dashboard state to localStorage
  function saveDashboardState() {
    const widgets_temp = document.querySelectorAll('.drag_widget');
    const widgetOrder = Array.from(widgets_temp).map(widget => widget.id);
    localStorage.setItem('widgetOrder', JSON.stringify(widgetOrder));
  }

  // Load the dashboard state from localStorage
  function loadDashboardState() {
    const dashboard_order = `<?php echo $dashboard_order; ?>`;
    if (!dashboard_order) return;
    const dashboard = document.querySelector('#drag_dashboard');
    const widgetOrder = JSON.parse(dashboard_order);
    if (widgetOrder) {
      widgetOrder.forEach(widgetId => {
        const widget = document.getElementById(widgetId);
        if (widget) {
          dashboard.appendChild(widget);
        }
      });
    }
  }

  function save_dashboard_order() {
    const widget_order = localStorage.getItem('widgetOrder');
    $.ajax({
      url: '<?php echo site_url('dashboard/save_dashboard_order'); ?>',
      data: {'dashboard_order': widget_order},
      dataType: "json",
      type: "post",
      success: function (data) {
        if (data) {
            if (widget_order) {
              $(function(){
              new PNotify({
                title: 'Success',
                text: 'Dashboard order saved successfully!',
                type: 'success',
              });
            });
          } else {
            $(function(){
              new PNotify({
                title: 'Success',
                text: 'Dashboard order reset successfully!',
                type: 'success',
              });
            });
          }
        }
        // location.reload();
      },
      error: function (err) {
        console.log(err);
      },
      complete: function() {
        remove_edit_dashboard();
			}
    });
  }

  function close_without_saving() {
    bootbox.confirm({
      title: "Reset Dashboard?",
      message: "All changes will be lost. Do you want to continue?",
      className:'widthadjust',
      width: '50%',
      buttons: {
        confirm: {
          label: 'Yes',
          className: 'btn-success'
        },
        cancel: {
          label: 'No',
          className: 'btn-danger'
        }
      },
      callback: function (result) {
        if (result) {
          remove_edit_dashboard();
          location.reload();
        }
      }
    });
  }

  function reset_dashboard_order() {
    bootbox.confirm({
      title: "Reset Dashboard?",
      message: "Reset Dashboard to factory settings. Do you want to continue?",
      className:'widthadjust',
      width: '50%',
      buttons: {
        confirm: {
          label: 'Yes',
          className: 'btn-success'
        },
        cancel: {
          label: 'No',
          className: 'btn-danger'
        }
      },
      callback: function (result) {
        if (result) {
          localStorage.setItem('widgetOrder', null);
          save_dashboard_order();
        }
      }
    });
  }
</script>

<?php $this->load->view('dashboard/enforce_flash_news.php'); ?>
<?php $this->load->view('dashboard/staff/blocks/_css.php'); ?>
<?php $this->load->view('dashboard/staff/blocks/_script.php'); ?>
<style>
  .carousel-control.left span, .carousel-control.right span {
      background-color: transparent;
  }
  
  .widthadjust{
  width:600px;
  margin:auto;
  }

  #myfirstchart>svg{
    width: 26vw;
    left: -20%;
  }

  #staffCat>svg{
    width: 55vw;
    left: -120% !important;
  }

  #studentCat>svg{
    width: 55vw;
    left: -123% !important;
  }

  .desktop-grid{
    width:100%;
    display: grid;
    grid-template-columns: repeat(3, 32.4%);
    grid-auto-flow: row dense;
    grid-gap: 1rem 1.7rem;
  }

  .span-2{
    grid-column: span 2;
  }

  .btn-editDash-save {
    color: #6893ca !important;
    background-color: transparent;
    border-color: #6893ca;
    border-radius: 0.7rem;
    padding: 4px 14px;
    font-weight: bold;
    border: solid;
  }

  .btn-editDash-close {
    color: #fe970a !important;
    background-color: transparent;
    border-color: #fe970a;
    border-radius: 0.7rem;
    padding: 4px 14px;
    font-weight: bold;
    border: solid;
  }

  .btn-editDash-reset {
    color: #e04b4a !important;
    background-color: transparent;
    border-color: #e04b4a;
    border-radius: 0.7rem;
    padding: 4px 14px;
    font-weight: bold;
    border: solid;
  }

  .box-small_active {
    height:50px !important;
    overflow: hidden;
    margin-bottom:10px;
  }

  .edit-box_active{
    border: solid;
    display: flow-root;
    padding: 10px 0px;
    border-radius: 0.7rem;
    border-style: dashed solid;
    animation: 4s infinite glow;
    /* height: 90vh !important; */
  }
@keyframes glow {
  0% {
    border-color: #6893ca;
  }
  10%{
    border-color: #fe970a;
  }
  20%{
    border-color: #6893ca;    
  }
  30%{
    border-color: #fe970a;
  }
  40%{
    border-color: #6893ca;    
  }
  50%{
    border-color: #fe970a;
  }
  60%{
    border-color: #6893ca;    
  }
  70% {
    border-color: #fe970a;
  }
  80% {
    border-color: #6893ca;
  }
  90% {
    border-color: #fe970a;
  }
  100%{
    border-color: #6893ca;
  }
}
@-webkit-keyframes glow {
  0% {
    border-color: #6893ca;
  }
  10%{
    border-color: #fe970a;
  }
  20%{
    border-color: #6893ca;    
  }
  30%{
    border-color: #fe970a;
  }
  40%{
    border-color: #6893ca;    
  }
  50%{
    border-color: #fe970a;
  }
  60%{
    border-color: #6893ca;    
  }
  70% {
    border-color: #fe970a;
  }
  80% {
    border-color: #6893ca;
  }
  90% {
    border-color: #fe970a;
  }
  100%{
    border-color: #6893ca;
  }
}

"hello world"
</style>