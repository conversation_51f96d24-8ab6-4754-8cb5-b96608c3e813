<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;"
	id="student_attendance_day_v2_summary">
	<div class="card-header panel_heading_new_style_staff"
		style="border-top-left-radius: 8px;border-top-right-radius: 8px">
		<div class="card-title card-title-new-style">
			<span> Student Day Attendance V2 &nbsp;</span>
		</div>
	</div>
	<div class="card-body padding-1" style="padding:5px;overflow: auto;">
		<div class="row mx-0">
			<div class="col-md-12 mb-2">
				<?php if (isset($attendance_type)) { ?>
					<?php if ($attendance_type == 'holiday') { ?>
						<div class="alert alert-info">
							<i class="fa fa-calendar"></i> <?php echo $attendance_message; ?>
						</div>
					<?php } elseif ($attendance_type == 'event') { ?>
						<div class="alert alert-warning">
							<i class="fa fa-star"></i> <?php echo $attendance_message; ?>
						</div>
					<?php } elseif ($attendance_type == 'no_sections') { ?>
						<div class="alert alert-secondary">
							<i class="fa fa-info-circle"></i> <?php echo $attendance_message; ?>
						</div>
					<?php } elseif ($attendance_type == 'no_permission') { ?>
						<div class="alert alert-warning">
							<i class="fa fa-lock"></i> <?php echo $attendance_message; ?>
						</div>
					<?php } elseif ($attendance_type == 'not_taken') { ?>
						<div class="alert alert-danger">
							<i class="fa fa-exclamation-triangle"></i> <?php echo $attendance_message; ?>
						</div>
					<?php } else { ?>
						<span style="font-size: 16px;margin-right:2rem;">
							<i class="fa fa-square" style="color: #95b75d;"></i>&nbsp;#Present: <b><?php echo $present; ?></b>
						</span>&nbsp;
						<span style="font-size: 16px;margin-right:2rem;">
							<i class="fa fa-square" style="color: #fe970a;"></i>&nbsp;#Absent: <b><?php echo $absent; ?></b>
						</span>&nbsp;
					<?php } ?>
				<?php } else { ?>
					<span style="font-size: 16px;margin-right:2rem;">
						<i class="fa fa-square" style="color: #95b75d;"></i>&nbsp;#Present: <b><?php echo isset($present) ? $present : 0; ?></b>
					</span>&nbsp;
					<span style="font-size: 16px;margin-right:2rem;">
						<i class="fa fa-square" style="color: #fe970a;"></i>&nbsp;#Absent: <b><?php echo isset($absent) ? $absent : 0; ?></b>
					</span>&nbsp;
				<?php } ?>
			</div>

			<?php if (isset($attendance_type) && in_array($attendance_type, ['holiday', 'event', 'no_sections', 'no_permission', 'not_taken'])) { ?>
				<div class="col-md-12 text-center" style="padding: 50px;">
					<?php if ($attendance_type == 'holiday') { ?>
						<i class="fa fa-calendar fa-3x text-info"></i>
						<h4 class="mt-3">Holiday</h4>
					<?php } elseif ($attendance_type == 'event') { ?>
						<i class="fa fa-star fa-3x text-warning"></i>
						<h4 class="mt-3">Special Event</h4>
					<?php } elseif ($attendance_type == 'no_sections') { ?>
						<i class="fa fa-info-circle fa-3x text-secondary"></i>
						<h4 class="mt-3">No Sections Assigned</h4>
					<?php } elseif ($attendance_type == 'no_permission') { ?>
						<i class="fa fa-lock fa-3x text-warning"></i>
						<h4 class="mt-3">Access Denied</h4>
						<p class="text-muted">You do not have permission to view attendance data.</p>
					<?php } else { ?>
						<i class="fa fa-exclamation-triangle fa-3x text-danger"></i>
						<h4 class="mt-3">Attendance Not Taken</h4>
						<p class="text-muted">Please take attendance for your assigned sections.</p>
					<?php } ?>
				</div>
			<?php } else { ?>
				<div class="col-md-12 px-0">
					<?php if (isset($class_wise_data) && !empty($class_wise_data)) { ?>
						<!-- Class-wise display for users with TAKE_ALL_SECTION_ATTENDANCE permission -->
						<div class="card mx-3" style="border-radius:8px;">
							<div class="card-header panel_heading_new_style_staff"
								style="border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom: solid 1px #eee !important;">
								<div class="row mx-0">
									<div class="col-md-6 text-center" style="padding: 10px 2px;">
										<strong>Class-wise Attendance Summary</strong>
									</div>
									<div class="col-md-6 text-center" style="padding: 10px 2px;">
										<span style="font-size: 14px;">
											<i class="fa fa-square" style="color: #95b75d;"></i> Present: <b><?php echo isset($present) ? $present : 0; ?></b> |
											<i class="fa fa-square" style="color: #fe970a;"></i> Absent: <b><?php echo isset($absent) ? $absent : 0; ?></b>
										</span>
									</div>
								</div>
							</div>
							<div class="panel-body" style="height: 200px;padding: 8px 2px; overflow-y: scroll;">
								<?php foreach ($class_wise_data as $class_key => $class_data) { ?>
									<div class="row mb-2" style="padding: 5px; border-bottom: 1px solid #eee;">
										<div class="col-md-4">
											<strong><?php echo $class_data['class_name']; ?></strong>
											<br>
											<small class="text-muted">
												P: <?php echo $class_data['total_present']; ?> |
												A: <?php echo $class_data['total_absent']; ?>
											</small>
										</div>
										<div class="col-md-8">
											<div class="row">
												<?php foreach ($class_data['sections'] as $section_key => $section_data) { ?>
													<div class="col-md-6 mb-1">
														<div class="card" style="padding: 5px; font-size: 12px; border: 1px solid #ddd;">
															<strong><?php echo $section_data['section_name']; ?></strong>
															<div>
																<span style="color: #95b75d;">P: <?php echo $section_data['present']; ?></span> |
																<span style="color: #fe970a;">A: <?php echo $section_data['absent']; ?></span>
															</div>
														</div>
													</div>
												<?php } ?>
											</div>
										</div>
									</div>
								<?php } ?>
							</div>
						</div>
					<?php } else { ?>
						<!-- Section-wise display for users with only TAKE_ATTENDANCE permission -->
						<div class="card mx-3" style="border-radius:8px;">
							<div class="card-header panel_heading_new_style_staff"
								style="border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom: solid 1px #eee !important;">
								<div class="row mx-0">
									<div class="col-md-12 text-center" style="padding: 10px 2px;">
										<strong>My Sections Attendance Summary</strong>
									</div>
								</div>
							</div>
							<div class="panel-body" style="height: 180px;padding: 8px 2px; overflow-y: scroll;">
								<div class="row" style="padding: 5px;">
									<div class="col-md-6 text-center">
										<div class="progress-circle" style="background: #95b75d; color: white; border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
											<div>
												<div style="font-size: 24px; font-weight: bold;"><?php echo isset($present) ? $present : 0; ?></div>
												<div style="font-size: 12px;">Present</div>
											</div>
										</div>
									</div>
									<div class="col-md-6 text-center">
										<div class="progress-circle" style="background: #fe970a; color: white; border-radius: 50%; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
											<div>
												<div style="font-size: 24px; font-weight: bold;"><?php echo isset($absent) ? $absent : 0; ?></div>
												<div style="font-size: 12px;">Absent</div>
											</div>
										</div>
									</div>
								</div>
								<div class="row mt-3" style="padding: 5px;">
									<div class="col-md-12 text-center">
										<p class="text-muted">
											Status: <strong><?php echo isset($attTaken) ? $attTaken : 'Not Taken'; ?></strong>
										</p>
										<?php if (isset($numberOfStudents) && $numberOfStudents > 0) { ?>
											<p class="text-muted">
												Total Students: <strong><?php echo $numberOfStudents; ?></strong>
											</p>
										<?php } ?>
									</div>
								</div>
							</div>
						</div>
					<?php } ?>
				</div>
			<?php } ?>
		</div>
	</div>
</div>

<style>
.progress-circle {
	transition: all 0.3s ease;
}
.progress-circle:hover {
	transform: scale(1.1);
}
</style>