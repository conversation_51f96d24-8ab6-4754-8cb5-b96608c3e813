<div class="card" style="width: 100%;border-radius: 8px;height: 31rem;margin-bottom: 8px;"
	id="student_attendance_summary">
	<div class="card-header panel_heading_new_style_staff"
		style="border-top-left-radius: 8px;border-top-right-radius: 8px">
		<div class="card-title card-title-new-style">
			<span> Student Day Attendance &nbsp;</span>

		</div>
	</div>
	<div class="card-body padding-1" style="padding:5px;overflow: auto;">
		<div class="row mx-0">
			<div class="col-md-12 mb-2">
				<a href="<?= site_url('/reports/attendance/student_attendance/special_case_report') ?>" <span
					style="font-size: 16px;margin-right:2rem;"><i class="fa fa-square"
						style="color: #95b75d;"></i>&nbsp;#Present: <b>
						<?php echo $present; ?>
					</b> </span>&nbsp;
					<span style="font-size: 16px;margin-right:2rem;"><i class="fa fa-square"
							style="color: #fe970a;"></i>&nbsp;#Absent: <b>
							<?php echo $absent; ?>
						</b> </span>&nbsp;
					<span style="font-size: 16px;margin-right:2rem;"><i class="fa fa-square"
							style="color: #E04B4A;"></i>&nbsp;#Not Taken: <b>
							<?php echo $attNotTaken; ?>
						</b> </span></a>
			</div>
			<div class="col-md-12 px-0">
				<div class="card mx-3" style="border-radius:8px;">
					<div class="card-header panel_heading_new_style_staff"
						style="border-top-left-radius: 8px;border-top-right-radius: 8px;border-bottom: solid 1px #eee !important;">
						<div class="row mx-0">
							<div class="col-md-2 text-center"
								style="padding: 2px 2px;font-size: 16px; font-weight: bold;">
								<span style="font-size: 12px;">Sections</span><br>
								<?php echo $attTaken . '/' . $totalSections ?>
							</div>
							<div class="col-md-9 text-center" style="padding: 10px 2px;">
								<div class="row">
									<?php $count = count($sectionName); ?>
									<?php foreach ($sectionName as $key => $sec) { ?>
										<!-- <div class="col-md-<?php //echo 12 / $count ?>"><?php //echo $sec->section_name ?></div> -->
										<div style="width:<?php echo 100 / $count ?>%;float:left;">
											<?php echo $sec->section_name ?>
										</div>
									<?php } ?>
								</div>
							</div>
						</div>
					</div>
					<div class="panel-body" style="height: 180px;padding: 8px 2px; overflow-y : scroll;">
						<?php
						foreach ($att_data['data'] as $key => $class) { ?>
							<div class="row" style="padding: 5px;">
								<div class="col-md-2 text-center" style="font-size: 16px;">
									<?php echo $class[0]->cname ?>
								</div>
								<div class="col-md-9">
									<div class="progress">
										<?php foreach ($sectionName as $key => $sec) {
											$isSectionFound=0;
											foreach ($class as $k => $section) {
												if($sec->section_name==$section->csname){
													$isSectionFound=1;
													if (isset($section->attendanceTaken) && $section->attendanceTaken == 'yes') {
														$parameter = date('d-m-Y') . '_' . $section->class_section;
														?>
														<a class="progress-bar bg-success" role="progressbar"
															style="width:<?php echo 100 / $count ?>%;border-right:1px solid white;"
															href="<?php echo site_url('reports/attendance/student_attendance/requestDayAttendance/' . $parameter) ?>"></a>
													<?php } else { ?>
														<div class="progress-bar bg-danger" role="progressbar"
															style="width:<?php echo 100 / $count ?>%;border-right:1px solid white;">
														</div>
													<?php }
												}
												// inner loop
											}

											if(!$isSectionFound){
												echo '<div class="progress-bar bg-danger" role="progressbar"
															style="border-right:1px solid white;">
														</div>';
											}
											// outer loop
										} ?>
									</div>
								</div>
							</div>
						<?php }
						?>
					</div>


				</div>
			</div>
		</div>
	</div>
</div>






<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css">
<script src="//cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js"></script>
<style>
	.progress {
		height: 10px;
		margin: 5px 2px;
		border-radius: 0px;
	}
</style>